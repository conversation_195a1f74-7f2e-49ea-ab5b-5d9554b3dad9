#ifndef __VIDEO_H__
#define __VIDEO_H__

#include "device.h"
#include "camera.h"
#include "isp_dev.h"
#include "printf.h"
#include "hw_timer.h"
#include "eva.h"
#include "dma_copy.h"
#include "msg.h"
#include "fs.h"
#include "imc.h"

struct uvc_video_data {
    u8 idx;
    u8 fmt;
    u8 fps;
    u8 frame_id;
    u16 width;
    u16 height;
    u32 offset;
};

extern const struct camera_platform_data camera0_data;
int video_open(const char *camera_name, int idx, int fmt, int frame_id, int fps, u16 width, u16 height);
int video_put_buf(void *buf, int len, u32 arg);
int video_get_buf(void *buf, int len, u32 *arg);
int video_get_buf2(void **buf, int len, u32 *arg);
void video_close();
u32 video_get_speed();
void imc_osd_open(u16 x_start, u16 y_start);
void set_isp_debug_state(u8 state);
void *get_video_device();
#endif

