#include "sc202cs_ae.h"
#include "sc202cs.h"

#include "isp_alg.h"
#include "printf.h"


extern u32 sc202cs_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain);
extern u32 sc202cs_mipi_set_shutter(isp_ae_shutter_t *shutter);
extern void *sc202cs_mipi_get_ae_curve(u32 type, u32 fps, int *min_ev, int *max_ev);

const static u32 sc202cs_mipi_ae_variable_fps[][AE_CURVE_INFO_MAX] = {
    // EV      ExpoTime     ISO     INTERPOLATE
    {9102000,     30,       100,    AE_INTERP_EXP},  // lv = 20
    {27306,    70000,       100,    AE_INTERP_GAIN}, // lv = 10.74
    {13653,    90000,       100,    AE_INTERP_GAIN},
    {6826,     90000,       133,    AE_INTERP_GAIN},
    {3413,     90000,       200,    AE_INTERP_GAIN},
    {1707,     90000,       320,    AE_INTERP_GAIN},
    {853,      90000,       640,    AE_INTERP_GAIN},
    {427,      90000,      1280,    AE_INTERP_GAIN},
    {213,      90000,      2560,    AE_INTERP_GAIN},
    {106,      90000,      5120,    AE_INTERP_GAIN},
    {53,       90000,     10240,    AE_INTERP_GAIN},
    {26,       90000,     20480,    AE_INTERP_GAIN},
    {13,       90000,     40960,    AE_INTERP_GAIN},
    {AE_CURVE_END, 0,         0,              0}
};

const static u32 sc202cs_mipi_ae_50hz_30fps[][AE_CURVE_INFO_MAX] = {
    // EV      ExpoTime     ISO     INTERPOLATE
    {9102000,     30,       100,    AE_INTERP_EXP},  // lv = 20
    {27306,    10000,       100,    AE_INTERP_GAIN}, // lv = 10.74
    {13653,    20000,       100,    AE_INTERP_GAIN},
    {6826,     30000,       133,    AE_INTERP_GAIN},
    {3413,     60000,       266,    AE_INTERP_GAIN},
    {1707,     60000,       533,    AE_INTERP_GAIN},
    {853,      60000,      1066,    AE_INTERP_GAIN},
    {427,      60000,      2133,    AE_INTERP_GAIN},
    {213,      60000,      4266,    AE_INTERP_GAIN},
    {106,      60000,      8533,    AE_INTERP_GAIN},
    {53,       60000,     17066,    AE_INTERP_GAIN},
    {26,       60000,     34132,    AE_INTERP_GAIN},
    {13,       60000,     68264,    AE_INTERP_GAIN},

    {AE_CURVE_END, 0,         0,              0}
};

// 添加15FPS的曝光曲线
const static u32 sc202cs_mipi_ae_50hz_15fps[][AE_CURVE_INFO_MAX] = {
    // EV      ExpoTime     ISO     INTERPOLATE
    {9102000,     30,       100,    AE_INTERP_EXP},  // lv = 20
    {27306,    10000,       100,    AE_INTERP_GAIN}, // lv = 10.74
    {13653,    20000,       100,    AE_INTERP_GAIN},
    {6826,     30000,       100,    AE_INTERP_GAIN},
    {3413,     40000,       200,    AE_INTERP_GAIN},
    {1707,     50000,       400,    AE_INTERP_GAIN},
    {853,      60000,       800,    AE_INTERP_GAIN},
    {427,      70000,      1600,    AE_INTERP_GAIN},
    {213,      80000,      3200,    AE_INTERP_GAIN}, // lv = 3.73
    {106,      90000,      6400,    AE_INTERP_GAIN},
    {53,       100000,    12800,    AE_INTERP_GAIN},
    {26,       100000,    25600,    AE_INTERP_GAIN},
    {13,       100000,    25600,    AE_INTERP_GAIN},

    {AE_CURVE_END, 0,         0,              0}
};

static u32 user_ev_value_table[] = {86, 98, 112, 128, 144, 162, 182}; //q7

static u8 ae_weight_cust[] = {
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1
};

static isp_ae_params_t sc202cs_mipi_ae_params = {

    .ae_max_ev = AE_MAX_EV,
    .ae_min_ev = AE_MIN_EV,
    .ae_init_ev = AE_INIT_EV,

    .ae_curve_type = AE_CURVE_TYPE,

    .ae_target = AE_TARGET_LUMA,

    .ae_phase_comp = AE_PHASE_COMP,

    .ae_conver_h = AE_CONVER_H,
    .ae_conver_l = AE_CONVER_L,
    .ae_diver_h = AE_DIVER_H,
    .ae_diver_l = AE_DIVER_L,

    .ae_ratio_max_h = AE_RATIO_MAX_H,
    .ae_ratio_max_l = AE_RATIO_MAX_L,

    .ae_ratio_max2_h = AE_RATIO_MAX2_H,
    .ae_ratio_max2_l = AE_RATIO_MAX2_L,

    .ae_ratio_slope = AE_RATIO_SLOPE,

    .ae_expline_th = AE_EXPLINE_TH,

    .ae_luma_smooth_num = AE_LUMA_SMOOTH_NUM,

    .ae_user_ev_value_table = (u32 *) &user_ev_value_table,
    .ae_user_ev_value_table_size = sizeof(user_ev_value_table) / sizeof(u32),
    .ae_user_ev_value = 0, // centered to zero

    .ae_init_weight_type = AE_INIT_WEIGHT_TYPE,
    .ae_win_cust_weights = (u8 *) &ae_weight_cust,

    .ae_ops = {
        .calc_shutter = sc202cs_mipi_calc_shutter,
        .set_shutter = sc202cs_mipi_set_shutter,
        .get_ae_curve = sc202cs_mipi_get_ae_curve,
    },
};

static void print_ae_curve(u32 (*ae_curve)[4])
{
    for (int i = 0; i < 13; i++)
    {
        printf("idx:%d %d %d %d %d\n", i,
            (int)(ae_curve[i][0]),
            (int)(ae_curve[i][1]),
            (int)(ae_curve[i][2]),
            (int)(ae_curve[i][3]));
    }
}

static void print_ae_shutter(isp_ae_shutter_t *shutter)
{
    printf("ae_exp_line:%d ae_exp_clk:%d ae_gain:%d\n", 
    (int)shutter->ae_exp_line,
    (int)shutter->ae_exp_clk,
    (int)shutter->ae_gain
    );
}

void sc202cs_mipi_ae_ev_init(u32 fps)
{
    u32 time, gain;
    int minev, maxev;
    static isp_ae_shutter_t ae_shutter;
    u32(*ae_curve)[AE_CURVE_INFO_MAX];

    ae_curve = sc202cs_mipi_get_ae_curve(AE_CURVE_TYPE, fps, &minev, &maxev);
    isp_ae_curve_interp(ae_curve, AE_INIT_EV, &time, &gain);

    sc202cs_mipi_calc_shutter(&ae_shutter, time, gain);
    sc202cs_mipi_set_shutter(&ae_shutter);
}

void *sc202cs_mipi_get_ae_curve(u32 type, u32 fps, int *min_ev, int *max_ev)
{
    void *curve;

    *min_ev = AE_MIN_EV;
    *max_ev = AE_MAX_EV;
    curve = (void *)&sc202cs_mipi_ae_50hz_15fps;

    if (type == AE_CURVE_50HZ) {
#if sc202cs_FPS_VARIABLE
        *min_ev = AE_MIN_EV;
        *max_ev = AE_MAX_EV;

        printf("use 50hz variable fps\n");
        curve = (void*)&sc202cs_mipi_ae_50hz_variable_fps[0];
#else
        *min_ev = AE_MIN_EV;
        *max_ev = AE_MAX_EV;

        if (fps == 30) {
            curve = (void *)&sc202cs_mipi_ae_50hz_30fps[0];
        printf("use 50hz 30 fps\n");

        } else if (fps == 15) {
            curve = (void *)&sc202cs_mipi_ae_50hz_15fps[0];
        printf("use 50hz 15 fps\n");

        }
#endif // sc202cs_FPS_VARIABLE
    } else {
        *min_ev = AE_MIN_EV;
        *max_ev = AE_MAX_EV;

        curve = (void *)&sc202cs_mipi_ae_50hz_30fps[0];
    }

    return curve;
}

void *sc202cs_mipi_get_ae_params()
{
    return (void *)&sc202cs_mipi_ae_params;
}
