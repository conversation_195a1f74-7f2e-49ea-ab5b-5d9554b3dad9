#include "app_config.h"
#include "device.h"
#include "printf.h"
#include "gpio.h"
#include "iic.h"
#include "sd_host_api.h"
#include "spi.h"
#include "isp_dev.h"
#include "camera.h"
#include "spiflash.h"
#include "cpu.h"

#include "key_driver.h"
#include "adkey.h"
#include "iokey.h"
#include "usb_audio.h"

#ifdef CONFIG_BOARD_AC5316A_DEV_20211231

 ////#define CONFIG_UVC_PARK_IO        (IO_PORTA_01)     //倒车检测 IO
#define CONFIG_UVC_LIGHT_IO       (IO_PORTA_03)     //补光灯 IO
// 功放控制
typedef enum pa_ctrl
{
    PA_DISABLE = 0,
    PA_ENABLE = 1,
} pa_ctrl_t;
#define CONFIG_PA_ENABLE_IO            (IO_PORTB_07)     //功放MUTE脚，高电平打开
#define CONFIG_SPK_FRAME_DROP_CNT      (100)

#define CONFIG_CAM_DEVICE_NAME    "cam0"

struct hw_iic_config _hw_iic = {
    .id = 0, //iic0
    //mipi iic
    .sda = IO_PORTA_09,
    .scl = IO_PORTA_08,
    .baudrate = 300 * 1000, //波特率
    .hdrive = 0,
    .io_filter = 0,
    .io_pu = 1, //上拉
};
/*
struct software_iic _sw_iic = {
    .dat_pin = IO_PORTB_08,
    .clk_pin = IO_PORTB_07,
    .sw_iic_delay = 0,
};
 */
const struct uvc_reso_info board_jpg_fmt = {
    // when use YUV420, jpeg compress is too slow, so we can only output 320*240 resolution
#ifdef CONFIG_USE_YUV420
    .num = 1,
    .reso = {
        {320, 240, 30, 25},
    },
#elif (CONFIG_IS_PRODUCT_S30) || defined(CONFIG_PRODUCT_AC53XX_EVB)
    .num = 3,
    .reso = {
        //width, height, fps, fps2
        #if defined (CONFIG_PRODUCT_S30_OUTPUT_864x480)
        {864, 480, 30, 25},
        {320, 240, 30, 25},
        {1280, 720, 30, 25},
        #elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)
        {640, 480, 30, 25},
        {864, 480, 30, 25},
        {1280, 720, 30, 25},
        #else
        {640, 480, 30, 25},
        {320, 240, 30, 25},
        {1280, 720, 30, 25},
        #endif
    },
#elif defined (CONFIG_PRODUCT_YU7_VM0)
    .num = 3,
    .reso = {
        {640, 480, 30, 25},
        {320, 240, 30, 25},
        {1280, 720, 30, 25},
    },
#else
    .num = 6,
    .reso = {
        //width, height, fps, fps2
        {1280, 720,20, 16},
        {864, 480, 20, 16},
        {800, 480, 20, 16},
        {640, 480, 20, 16},
        {480, 320, 20, 16},
        {320, 240, 20, 16},
    },
#endif
};
#ifdef CONFIG_YUYV_ENABLE
const struct uvc_reso_info board_yuv_fmt = {
    .num = 2,
    /* .num = 1, */
    .reso = {
        //width, height, fps, fps2
        /* {1280, 720, 10, 0}, */
        {640, 480, 15, 0},
        {320, 240, 20, 0},
    },
};
#endif

#ifdef SDC_EN
#include "sd_host_api.h"
static const struct sdmmc_platform_data sd0_data = {
    .port                   = 'A',
    .data_width             = 1,
    .speed                  = 32000000,
    .detect_mode            = SD_CMD_DECT,
    .detect_func            = sdmmc_0_cmd_detect,
    .detect_io              = IO_PORTA_11,
    .power                  = NULL,
    .priority               = 3,
    .irq 					= IRQ_SD0_IDX,
    .sfr                    = JL_SDC,
    .port_init 				= sdmmc_0_port_init,
    .detect_time_interval 	= 250,
    .detect_timeout     	= 2000,
    .sdio = {
        .cmd_io                 = IO_PORTA_05,
        .clk_io                 = IO_PORTA_06,
        .dat0_io                = IO_PORTA_07,
        .dat1_io                = NULL,
        .dat2_io                = NULL,
        .dat3_io                = NULL,
    },
};
#endif



const struct camera_platform_data camera0_data = {
    .xclk_gpio      = IO_PORTA_05,
    .reset_gpio     = IO_PORTA_07,
    .online_detect  = NULL,
    .pwdn_gpio      = IO_PORTA_06,
    .power_value    = 0,
    .interface      = SEN_INTERFACE_CSI2,
    .csi2 = {
        .data_lane_num = 1,
        .clk_rmap = CSI2_X0_LANE,
        .clk_inv = 0,
        .d0_rmap = CSI2_X1_LANE,
        .d0_inv = 0,
        .tval_hstt = 12,
        .tval_stto = 12,
    }
};


static const struct adkey_platform_data adkey_data = {
    .io = IO_PORTB_09,
    .ad_channel = ADC_CH11_PB9,
    .base_cnt = 4,
    .long_cnt = 75,
    .hold_cnt = 90,
    .scan_time = 10,
    .table = {
        .ad_value = {
            128,
            256,
            384,
            512,
            640,
            768,
            896,
            999,
        },
        .key_value = {
            KEY_MODE,	//0
            KEY_NONE,
            KEY_NONE,
            KEY_MENU,	//434
            KEY_NONE,
            KEY_UP,		//699
            KEY_DOWN,	//847
            KEY_OK,		//957
        },
    },
};

static const struct iokey_platform_data iokey_data = {
    .base_cnt = 4,
    .long_cnt = 75,
    .hold_cnt = 90,
    .scan_time = 10,
    .table = {
        .io = {
            IO_PORTB_00,
        },
        .key_value = {
            KEY_POWER,
        },
    },
};

/** camera device name **/
const char *get_board_camera_name(void)
{
    return CONFIG_CAM_DEVICE_NAME;
}
/** 补光灯控制函数 **/
#if 0
void set_board_led_light_io(u8 state)
{
#ifdef CONFIG_UVC_LIGHT_IO
    /* gpio_direction_output(CONFIG_UVC_LIGHT_IO, !state); */
    gpio_direction_output(CONFIG_UVC_LIGHT_IO, state);
#endif
}
#endif

/** LDO控制函数 **/
void set_board_ldo_power_init()
{
    //开启内部电源
    avdd18_ctl(AVDD18_1809, 1);
    mdelay(50);
    avdd28_ctl(AVDD28_2905, 1);

    /* avdd28_ctl(AVDD28_3205,1); */
    /* avdd18_ctl(AVDD18_1509,1); */
    /* sen_xclk_output(IO_PORTB_09, CLK_OUT0, 24); */

    // mipi iic io die enable
    gpio_set_die(IO_PORTA_09, 1);
    gpio_set_die(IO_PORTA_08, 1);
    //Test
    /* clk_out_to_io(IO_PORTB_02, 3, CLK_OUT3, PLL48M_CLK); */
    /* clk_out_to_io(IO_PORTB_03, 4, CLK_OUT3, PLL48M_CLK); */

    /* clk_out_to_io(IO_PORTB_04, 4, CLK_OUT1, PLL48M_CLK); */
    /* clk_out_to_io(IO_PORTB_06, 6, CLK_OUT3, PLL48M_CLK); */
    /* clk_out_to_io(IO_PORTB_08, 5, CLK_OUT3, PLL48M_CLK); */
    /* clk_out_to_io(IO_PORTA_00, 7, CLK_OUT3, PLL48M_CLK); */

}
int set_camera_freq(u32 frame_freq)
{
    isp_sen_t *c;
    void *camera_fd = get_video_device();
    void *isp_sen;
    if (camera_fd) {
        dev_ioctl(camera_fd, CAMERA_GET_SENSOR_HANDLE, (u32)&isp_sen);
        c = (isp_sen_t *)isp_sen;
        c->ops.set_size_fps(NULL, NULL, (u8 *)&frame_freq);
    }
    return 0;
}
#if UART_DEBUG
void uart_debug_init()
{
     debug_uart_init("PA01", 1000000, 0); //初始化串口
}
#endif

#ifdef FILL_LIGHT_ENABLE
void fill_light_on(void)
{
////#ifdef CONFIG_UVC_LIGHT_IO
    gpio_direction_output(CONFIG_UVC_LIGHT_IO, 1);
   // timer_pwm_init(JL_TIMER3, IO_PORTA_03, 20000, 5000, 0);
////#endif
}

void fill_light_off(void)
{
////#ifdef CONFIG_UVC_LIGHT_IO
    gpio_direction_output(CONFIG_UVC_LIGHT_IO, 0);
   // timer_pwm_init(JL_TIMER3, IO_PORTA_03, 20000, 5000, 0);
////#endif
}
#endif

/////////////////start
void hw_spk_enable(void)
{

    // gpio_direction_output(IO_PORTB_07, 1);
   //// printf("1111--------------------\r\n");
}
void hw_spk_close(void)
{
    // gpio_direction_output(IO_PORTB_07, 0);
   //// printf("2222--------------------\r\n");
}

void audio_spk_play_cb(u32 audio_frame_cnt)
{
    const u32 drop_cnt = CONFIG_SPK_FRAME_DROP_CNT; // 丢弃一些音频数据避免爆破音
    if (audio_frame_cnt == drop_cnt) {
        gpio_direction_output(CONFIG_PA_ENABLE_IO, PA_ENABLE);
        printf("pa enable\n");
    }
}

void audio_spk_close_cb(void)
{
    gpio_direction_output(CONFIG_PA_ENABLE_IO, PA_DISABLE);
    usb_audio_reset_audio_frame_cnt();
    usb_audio_clear_audio_data();
    printf("pa disable\n");
}

void board_init()
{

    devices_init();
#if (USB_DEVICE_CLASS_CONFIG == (VIDEO_CLASS | AUDIO_CLASS))
    usb_audio_register_spk_callback(audio_spk_play_cb, audio_spk_close_cb);
#endif
    set_board_ldo_power_init(); //开启电源
#ifdef SDC_EN
    extern void sd0_dev_detect(void *p);
    sys_timer_add(NULL, sd0_dev_detect, 100);
#endif

#ifdef CONFIG_UVC_PARK_IO
    parking_detect_init(CONFIG_UVC_PARK_IO); //倒车检测
#endif
////u32 tim0 = jiffies_to_msecs(get_jiffies());
////uac_speaker_stream_open(8000,1);
// extern int video_open(const char *camera_name, int idx, int fmt, int frame_id, int fps, u16 width, u16 height);
// video_open("camera0", 0, 0, 0, 20, 1280, 720);
// video_close();
////audio_mic_open(1, 8000);
////u32 tim1 = jiffies_to_msecs(get_jiffies());
////printf("--------MSG_UVC_OPEN out---1111:tim0:%d;tim1:%d\n",tim0,tim1);
}


extern const struct device_operations  camera_dev_ops;
extern const struct device_operations  key_dev_ops;

/***********注册设备表**********/
REGISTER_DEVICES(device_tables) = {
    /* {"iic0", &hw_iic_dev_ops, NULL}, //for sensor */
    /* {"swiic0", &sw_iic_dev_ops, NULL}, */
#if (defined SDC_EN)
    {"sd0",  &sd_dev_ops, (void *) &sd0_data},
#endif
    /* {"camera0",  &camera_dev_ops, (void *) &camera0_data}, */

    {"cam0",  &camera_dev_ops, (void *) &camera0_data}, //for mipi

#ifdef CONFIG_ADKEY_ENABLE
    {"adkey", &key_dev_ops, (void *) &adkey_data },
#endif
#ifdef CONFIG_IOKEY_ENABLE
    {"iokey", &key_dev_ops, (void *) &iokey_data },
#endif
};

#endif
