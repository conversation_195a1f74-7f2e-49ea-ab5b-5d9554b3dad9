#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "c2595.h"
#include "isp_alg.h"
#include "app_config.h"
#include "delay.h"

static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *c2595_mipi_get_ae_params();
extern void *c2595_mipi_get_awb_params();
extern void *c2595_mipi_get_iq_params();
extern void c2595_mipi_ae_ev_init(u32 fps);

static void set_again(u32 again);
static void set_dgain(u32 dgain);
static void set_shutter(u32 texp);

#define LINE_LENGTH_CLK     1600
#define FRAME_LENGTH        1200

#define ROW_TIME_NS         66667
#define INPUT_CLK  24
#define PCLK  90

static u32 cur_again = -1;
static u32 cur_dgain = -1;
static u32 cur_expline = -1;
static u32 cur_line_length_clk = -1;

static u32 line_length_clk = LINE_LENGTH_CLK;

static void *iic = NULL;

#define WRCMD 0x6c
#define RDCMD 0x6d

#define device_id_h 0x0000
#define device_id_l 0x0001
#define device_value_h 0x02
#define device_value_l 0x09

static const sensor_reg_t c2595_1280x720[] = {
    {0x3288, 0x50},
    {0x0400, 0x41},
    {0x0401, 0xa5},
    {0x0403, 0x36},
    {0x3885, 0x22},
    {0x3280, 0x28},
    {0x3284, 0xA3},
    {0x3288, 0x40},
    {0x328C, 0x48},
    {0x32aa, 0x05},
    {0x32ab, 0x08},
    {0x3C00, 0x43},
    {0x3C01, 0x03},
    {0x3218, 0x28},
    {0x3805, 0x08},
    {0x3808, 0x16},
    {0x3809, 0x96},
    {0x380a, 0x7d},
    {0x380b, 0xeb},
    {0x380e, 0x0d},
    {0x380c, 0x01},
    {0x0202, 0x04},
    {0x0203, 0x56},
    {0x3108, 0xcf},
    {0x3115, 0x30},
    {0x328b, 0xa9},
    {0x3295, 0x15},
    {0x328d, 0x09}, // 0a
    {0x3293, 0x01},
    {0x32a9, 0x3f},
    {0x3290, 0xb3},
    {0x32ad, 0x8a},
    {0x3216, 0x2f},
    {0x3217, 0x08}, // update 0827
    {0x3298, 0x48}, // remove the VFPN at some special again
    {0x3211, 0x10},
    {0x3216, 0x2f},
    {0x32af, 0x80}, // connect HVDD to 3287
    {0x3212, 0x4A}, // changed based on tuning results 201111
    {0x3286, 0x05}, // remove H line
    {0x3287, 0x46}, // improve H line
    {0x3881, 0x00}, // FIFO
    {0x0400, 0x47},
    {0x0404, 0x08},
    {0x0405, 0x80},
    {0x0406, 0x02},
    {0x0407, 0x80},
    {0x3403, 0x02},
    {0x3407, 0x07},
    {0x3411, 0x00},
    {0x3412, 0x01},
    {0x3415, 0x01},
    {0x3416, 0x01},
    {0x3500, 0x10},
    {0x3584, 0x02},
    {0xe000, 0x31},
    {0xe001, 0x08},
    {0xe002, 0x4f},
    {0xe00c, 0x31},
    {0xe00d, 0x08},
    {0xe00e, 0xef},
    {0xe018, 0x32},
    {0xe019, 0x93},
    {0xe01a, 0x03},
    {0xe01b, 0x32},
    {0xe01c, 0xa9},
    {0xe01d, 0x10},
    {0xe01e, 0x32},
    {0xe01f, 0xac},
    {0xe020, 0xff},
    {0xe021, 0x32},
    {0xe022, 0x90},
    {0xe023, 0xb6},
    {0xe024, 0x32},
    {0xe025, 0xad},
    {0xe026, 0x9f},
    {0xe027, 0x32},
    {0xe028, 0x11},
    {0xe029, 0x10},
    {0xe02a, 0x32},
    {0xe02b, 0x16},
    {0xe02c, 0x2f},
    {0x3500, 0x00},
    {0x3584, 0x22},
    {0x034c, 0x05},
    {0x034d, 0x00},
    {0x034e, 0x02},
    {0x034f, 0xd0},
    {0x3008, 0x00},
    {0x3009, 0xa8},
    {0x300a, 0x00},
    {0x300b, 0xf8},
    {0x3293, 0x03},
    {0x32a9, 0x10},
    {0x32ac, 0xf8},
    {0x3290, 0xb6},
    {0x32ad, 0x9f},
    {0x3211, 0x2f},
    {0x3216, 0x16},
    {0x0100, 0x01},
};

// 8位地址寄存器写入函数
unsigned char wr_c2595_reg_8bit(u8 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 16位地址寄存器写入函数，用于C2595传感器
unsigned char wr_c2595_reg(u16 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID & 0xff)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 8位地址寄存器读取函数
unsigned char rd_c2595_reg_8bit(u8 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

// 16位地址寄存器读取函数，用于C2595传感器
unsigned char rd_c2595_reg(u16 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __rend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID & 0xff)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}


/*************************************************************************************************
    sensor api
*************************************************************************************************/
// 配置传感器 输出分辨率 帧率
static u32 c2595_frame_rate = 30;
void c2595_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u8 v;

    printf("C2595 configuring %dx%d fps:%d\n", *width, (*height), *frame_freq);
    c2595_mipi_set_output_size(width, height, frame_freq);

    // 使用C2595传感器配置
    for (i = 0; i < sizeof(c2595_1280x720) / sizeof(sensor_reg_t); i++) {
        wr_c2595_reg(c2595_1280x720[i].addr, c2595_1280x720[i].val);
    }

    c2595_frame_rate = *frame_freq;
    if (c2595_frame_rate == 30) {
        wr_c2595_reg(0x0340, 0x04); // Frame Length (VTS) high
        wr_c2595_reg(0x0341, 0xD0); // VTS low = 0x04D0 = 1232
        wr_c2595_reg(0x0342, 0x09); // Line Length (HTS) high
        wr_c2595_reg(0x0343, 0x80); // HTS low = 0x0980 = 2432
    } else if (c2595_frame_rate == 15) {
        wr_c2595_reg(0x0340, 0x09);
        wr_c2595_reg(0x0341, 0xA0); // VTS = 2464
        wr_c2595_reg(0x0342, 0x09);
        wr_c2595_reg(0x0343, 0x80);
    }

    printf("[C2595] 配置完成，FPS设置为: %d fps\n", c2595_frame_rate);

    c2595_mipi_ae_ev_init(*frame_freq);
    cur_again = -1;
    cur_dgain = -1;
    cur_expline = -1;
    cur_line_length_clk = -1;
    return;
}

s32 c2595_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 c2595_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

s32 c2595_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i;

    // 根据C2595的传感器标识寄存器和数据
    wr_c2595_reg(0x0000, 0x00);
    for (i = 0; i < 3; i++) {
        rd_c2595_reg(device_id_h, &pid);
        rd_c2595_reg(device_id_l, &ver);
    }

    puts("C2595 Sensor PID \n");
    put_u8hex(pid);
    put_u8hex(ver);
    puts("\n");

    // C2595传感器ID检查（根据传感器文档）
    if (pid != device_value_h || ver != device_value_l) {
        puts("----not C2595 sensor-----\n");
        return -1;
    }
    puts("----hello C2595 sensor-----\n");
    return 0;
}

void c2595_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    // 1. RSTB拉低
    gpio_direction_output(reset_gpio, 0);

    delay_ms(1);

    // 5. RSTB拉高
    gpio_direction_output(reset_gpio, 1);

    // 6. PWDN拉低
    gpio_direction_output(pwdn_gpio, 0);

    delay_ms(1);
}


static u8 cur_sensor_type = 0xff;

s32 c2595_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{
    puts("\n\n c2595_mipi_check \n\n");

    printf("\n isp_dev C2595==========%d\n", isp_dev);
    if (!iic) {
        iic = dev_open("iic0", &_hw_iic);
        /* iic = dev_open("swiic0", &_sw_iic); */
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
    // printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    c2595_mipi_reset(isp_dev);

    if (0 != c2595_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

void resetStatic();
s32 c2595_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\n C2595_mipi_init \n\n");

    c2595_mipi_config_SENSOR(width, height, format, frame_freq);

    // 使能输出
    wr_c2595_reg(0x0100, 0x01);

    printf("C2595 init complete, streaming enabled\n");
    return 0;
}


static void updateforshutter()
{
}

// 增益表的阈值数组，用于快速查表
static const u16 gain_thresholds[] = {
    1024, 1088, 1152, 1216, 1280, 1344, 1408, 1472, 1536, 1600,
    1664, 1728, 1792, 1856, 1920, 1984, 2048, 2176, 2304, 2432,
    2560, 2688, 2816, 2944, 3072, 3200, 3328, 3456, 3584, 3712,
    3840, 3968, 4096, 4352, 4608, 4864, 5120, 5376, 5632, 5888,
    6144, 6400, 6656, 6912, 7168, 7424, 7680, 7936, 8192
};

// 寄存器值表，对应不同增益级别的寄存器配置
uint8_t regValTable[49][6] = {
    {0x50, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 0  32a9 32ac 32ad 3211 3216 3217
    {0x51, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 1
    {0x52, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 2
    {0x53, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 3
    {0x54, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 4
    {0x55, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 5
    {0x56, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 6
    {0x57, 0x4c, 0x9f, 0x15, 0x1a, 0x12}, // 7
    {0x58, 0x4c, 0xaf, 0x15, 0x1a, 0x12}, // 8
    {0x59, 0x4c, 0xaf, 0x15, 0x1a, 0x12}, // 9
    {0x5a, 0x4c, 0xaf, 0x15, 0x1a, 0x12}, // 10
    {0x5b, 0x4e, 0xaf, 0x15, 0x1a, 0x12}, // 11
    {0x5c, 0x4d, 0xaf, 0x15, 0x1a, 0x12}, // 12
    {0x5d, 0x4d, 0xaf, 0x15, 0x1a, 0x12}, // 13
    {0x5e, 0x4d, 0xaf, 0x15, 0x1a, 0x12}, // 14
    {0x5f, 0x4c, 0xaf, 0x15, 0x1a, 0x12}, // 15
    {0x60, 0x4e, 0xaf, 0x15, 0x1a, 0x12}, // 16
    {0x62, 0x4e, 0xaf, 0x15, 0x1a, 0x12}, // 17
    {0x64, 0x4e, 0xaf, 0x15, 0x1a, 0x12}, // 18
    {0x66, 0x4e, 0xaf, 0x15, 0x1a, 0x12}, // 19
    {0x68, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 20
    {0x6a, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 21
    {0x6c, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 22
    {0x6e, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 23
    {0x70, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 24
    {0x72, 0x3e, 0xaf, 0x15, 0x1a, 0x12}, // 25
    {0x74, 0x3d, 0xaf, 0x15, 0x1a, 0x12}, // 26
    {0x76, 0x3d, 0xaf, 0x15, 0x1a, 0x12}, // 27
    {0x78, 0x2d, 0xbf, 0x15, 0x1a, 0x12}, // 28
    {0x7a, 0x2d, 0xbf, 0x15, 0x1a, 0x12}, // 29
    {0x7c, 0x2c, 0xb0, 0x15, 0x1a, 0x12}, // 30
    {0x7e, 0x2d, 0xb0, 0x15, 0x1a, 0x12}, // 31
    {0x20, 0x4d, 0xb0, 0x15, 0x1a, 0x12}, // 32
    {0x22, 0x4d, 0xb0, 0x15, 0x1a, 0x12}, // 33
    {0x24, 0x4d, 0xb0, 0x15, 0x1a, 0x12}, // 34
    {0x26, 0x4d, 0xb0, 0x15, 0x1a, 0x12}, // 35
    {0x28, 0x3c, 0xb0, 0x15, 0x1a, 0x12}, // 36
    {0x2a, 0x3d, 0xb0, 0x15, 0x1a, 0x12}, // 37
    {0x2c, 0x3c, 0xb0, 0x15, 0x1a, 0x12}, // 38
    {0x2e, 0x3d, 0xb0, 0x15, 0x1a, 0x12}, // 39
    {0x30, 0x3d, 0xb0, 0x15, 0x1a, 0x12}, // 40
    {0x32, 0x3d, 0xb0, 0x15, 0x1a, 0x12}, // 41
    {0x34, 0x3e, 0xb0, 0x15, 0x1a, 0x12}, // 42
    {0x36, 0x3e, 0xb0, 0x15, 0x1a, 0x12}, // 43
    {0x38, 0x2e, 0xb0, 0x15, 0x1a, 0x12}, // 44
    {0x3a, 0x2e, 0xb0, 0x15, 0x1a, 0x12}, // 45
    {0x3c, 0x2d, 0xb0, 0x15, 0x1a, 0x12}, // 46
    {0x3e, 0x2d, 0xb0, 0x15, 0x1a, 0x12}, // 47
    {0x3f, 0x2f, 0xb0, 0x15, 0x1a, 0x12}, // 48
};

// 设置模拟增益（analog gain）
void set_again(u32 gain)
{
    // 如果增益没有变化，则不需要更新
    if (cur_again == gain) {
        return;
    }

    cur_again = gain;
    // 查表法确定索引
    u8 index;
    // 传入的gain范围是100 到 800
    // 需要转换成1024 到 8192
    // gain = gain * 1024 / 100;

    if (gain <= 1024) {
        index = 0;
    } else if (gain >= 8192) {
        index = 48;
    } else {
        u8 left, right;
        if (gain < 4608) { // 中间值
            left = 0;
            right = 32;
        } else {
            left = 32;
            right = 48;
        }

        // 再次缩小范围缩
        u8 mid = (left + right) >> 1;
        if (gain < gain_thresholds[mid]) {
            right = mid;
        } else {
            left = mid;
        }

        // 定位
        index = left;
        while (index < right && gain >= gain_thresholds[index + 1]) {
            index++;
        }
    }
    // index = 48;
    // 更新寄存器值
    wr_c2595_reg(0x32A9, regValTable[index][0]);
    wr_c2595_reg(0x32AC, regValTable[index][1]);
    wr_c2595_reg(0x32AD, regValTable[index][2]);
    wr_c2595_reg(0x3211, regValTable[index][3]);
    wr_c2595_reg(0x3216, regValTable[index][4]);
    wr_c2595_reg(0x3217, regValTable[index][5]);

    // printf("C2595 analog gain: %d (index=%u), gain_thresholds: %d\n", gain, index, gain_thresholds[index]);
}

// 设置数字增益（digital gain）
static void set_dgain(u32 dgain)
{
    if (cur_dgain == dgain) {
        return;
    }

    cur_dgain = dgain;

    /*
     * 数字增益寄存器为 12bit：
     * 0x0216: 高4位（有效位为bit[3:0]）
     * 0x0217: 低8位
     * dgain 值应为 256 表示 1.0 倍（可根据驱动标准修改）
     */
    if (dgain < 1) {
        dgain = 1;
    }

    if (dgain > 0xFFF) {
        dgain = 0xFFF;
    }

    u8 high = (dgain >> 8) & 0x0F;
    u8 low  = dgain & 0xFF;

    wr_c2595_reg(0x0216, high);
    wr_c2595_reg(0x0217, low);

    // printf("high = %x, low = %x,     %d     %d, dgain = %d\n", high, low, high, low, dgain);
}


// 计算增益
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    // C2595传感器增益规划
    // 模拟增益范围: 1024-8192 (1x-8x)
    // 数字增益用于超过8x的部分

    gain = gain * 1024 / 100;

    if (gain <= 8192) {
        *_again = gain;
        *_dgain = 100;
    } else {
        *_again = 8192;
        *_dgain = gain * 100 / 8192;
    }
}

// 设置曝光时间（单位：行）
static void set_shutter(u32 texp)
{
    if (cur_expline == texp) {
        return;
    }

    cur_expline = texp;

    wr_c2595_reg(0x0202, (texp >> 8) & 0xFF); // 高8位
    wr_c2595_reg(0x0203, texp & 0xFF);       // 低8位
}


// 计算曝光时间
u32 c2595_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;

#if C2595_FPS_VARIABLE
    // 使用更精确的计算方法
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#else
    texp = exp_time_us * PCLK * 1 / LINE_LENGTH_CLK;
#endif // C2595_FPS_VARIABLE

    // 保证最小曝光行数
    if (texp < 100) {
        texp = 100;
    }

    // 计算实际对应的曝光时间
    texp_align = (texp) * LINE_LENGTH_CLK / (PCLK * 1);

    if (texp_align < exp_time_us)
    {
        ratio = (exp_time_us) * (1 << 10) / texp_align;
        // printf("ratio = %d\n",ratio);
    }
    else
    {
        ratio = (1 << 10);
    }

    // 更新曝光参数
    shutter->ae_exp_line = texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;

    // printf("C2595 exp_time_us=%d, texp=%d, gain=%d->%d\n", exp_time_us, texp, gain, shutter->ae_gain);
    return 0;
}

// 设置曝光参数
u32 c2595_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    #define MAX_AGAIN    (8192)

    // 设置曝光参数
    u32 again, dgain;
    calc_gain((shutter->ae_gain), &again, &dgain);

    set_again(again);
    set_dgain(dgain);
    set_shutter(shutter->ae_exp_line);

    // 计算实际曝光时间用于日志显示
    u32 exp_time_us = (shutter->ae_exp_line * LINE_LENGTH_CLK) / PCLK;
    printf("C2595 AE: gain=%d, again=%d, dgain=%d, exp_lines=%d, exp_time_us=%d\n",
           shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us);

    return 0;
}

// 传感器休眠
void c2595_mipi_sleep()
{
}

// 传感器唤醒
void c2595_mipi_wakeup()
{
}

// 写寄存器接口
void c2595_mipi_wr_reg(u16 addr, u16 val)
{
    printf("C2595 update reg 0x%04X with 0x%02X\n", addr, val);
    wr_c2595_reg(addr, (u8)val);
}

// 读寄存器接口
u16 c2595_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_c2595_reg(addr, &val);
    return val;
}

// 注册摄像头设备
REGISTER_CAMERA(c2595_mipi) = {
    .logo               = "C2595",
    .isp_dev            = ISP_DEV_NONE,
    .in_format          = SEN_IN_FORMAT_BGGR,
    .out_format         = ISP_OUT_FORMAT_RAW,
    .mbus_type          = SEN_MBUS_CSI2,
    .mbus_config        = SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
#ifdef OUT_33_3FPS
    .fps                = 30,
    .real_fps           = (u32)(33.333333 * 65536),
#else
    .fps                = 15, // 30 // 15
#endif

    .sen_size           = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},
    .isp_size           = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},

#ifdef OUT_33_3FPS
    .cap_fps            = 30,
#else
    .cap_fps            = 15, // 30 // 15
#endif
    .sen_cap_size       = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},
    .isp_cap_size       = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},

    .ops                = {
        .avin_fps           = NULL,
        .avin_valid_signal  = NULL,
        .avin_mode_det      = NULL,
        .sensor_check       = c2595_mipi_check,
        .init               = c2595_mipi_init,
        .set_size_fps       = c2595_mipi_set_output_size,
        .power_ctrl         = c2595_mipi_power_ctl,

        .get_ae_params      = c2595_mipi_get_ae_params,
        .get_awb_params     = c2595_mipi_get_awb_params,
        .get_iq_params      = c2595_mipi_get_iq_params,

        .sleep              = c2595_mipi_sleep,
        .wakeup             = c2595_mipi_wakeup,
        .write_reg          = c2595_mipi_wr_reg,
        .read_reg           = c2595_mipi_rd_reg,
    }
};