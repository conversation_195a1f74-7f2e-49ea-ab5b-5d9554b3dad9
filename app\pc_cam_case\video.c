#include "video.h"
#include "jpeg_encoder.h"
#include "jpeg_abr.h"
#include "yuv_recorder.h"
#include "gpio.h"
#include "app_config.h"
#include "isp_customize.h"
#include "isp_scenes.h"
#include "osd.h"
#include "delay.h"
#include "clock.h"
#include "cpu.h"

#ifdef PC_CAM_CASE

//iso mode
#if UVC_ISO_MODE

#if !defined CONFIG_UVC_FOR_1080P_ENABLE && !defined CONFIG_NLPFIX_ENABLE

struct video_fh {
    u8 isp_debug: 1;
    u8 sub_isp: 1;
    u8 format: 1;
    u8 jpeg_set_fps_en: 1;
    u8 delay_out_frames;
    u8 *video_buf;
    cbuffer_t 	video_cbuf;
    int video_cbuf_size;
    int frame_end_byte;
    u8 cur_fps;
    void *camera;
    void *imc;
    u16 imc_width;
    u16 imc_height;

    u32 speed;
    u32 data_len;
    u32 time_stamp;
    u8 time_stamp_overflow;

    volatile u8 frame_state;
    /* volatile u8 one_frame; */
};

enum {
    FRAME_INIT_STATE = 0,
    FRAME_GAP_STATE, //帧间隔
    FRAME_END_STATE, //帧尾
};
struct video_fh _video ;

#define TAR_WIDTH   (1280)
#define TAR_HEIGHT   (720)

#define LINE_N   (18)
#define YUV_LINE_N   (2)

#ifdef CONFIG_NLPFIX_ENABLE
#define VIDEO_BUF_SIZE  (5*1024)
#else
#define VIDEO_BUF_SIZE  (15*1024)
#endif

#define JPEG_BIT_SIZE   (2*1024)

#define VIDEO_SPEED_CALC_TIM (200)
#define VIDEO_SPEED_CALC_FLITER (90)

#define __this   (&_video)

/* static u8 video_yuv_buf[TAR_WIDTH * LINE_N * 2] sec(.encode_buf) ALIGNE(64); //兼容yuv422 */
/* static u8 video_buf[VIDEO_BUF_SIZE] sec(.encode_buf) ALIGNE(64); */

/* #ifdef CONFIG_YUYV_ENABLE */
/* u8 video_yuv_dest_buf[YUV_MAX_WIDTH * YUV_LINE_N * 2] sec(.encode_buf) ALIGNE(64); */
/* #else */
/* u8 jpeg_bit_buf[JPEG_BIT_SIZE + 4] ALIGNE(64); */
/* #endif */

static u8 *video_yuv_buf;
static u8 *video_buf;
#ifdef CONFIG_YUYV_ENABLE
static u8 *video_yuv_dest_buf;
#else
static u8 *jpeg_bit_buf;
#endif
#define YUV_MAX_WIDTH (640)
static u8 video_alloc_buf[(TAR_WIDTH * LINE_N * 2) + YUV_MAX_WIDTH * YUV_LINE_N * 2 + VIDEO_BUF_SIZE] sec(.encode_buf) ALIGNE(64);


static void alloc_video_buf(u8 fmt, u16 width, u16 height)
{
    int buf_offset = 0;
    video_yuv_buf = video_alloc_buf;
    buf_offset += width * LINE_N * 2;
    memset(video_yuv_buf, 0, width * LINE_N * 2);
#ifdef CONFIG_YUYV_ENABLE
    video_yuv_dest_buf = &video_alloc_buf[buf_offset];
#else
    jpeg_bit_buf = &video_alloc_buf[buf_offset];
#endif
    if (fmt == 1) {
        //yuyv
        __this->video_cbuf_size = 0;
        return;
    } else {
        //mjpeg
        buf_offset += (JPEG_BIT_SIZE + 4);
    }
    video_buf = &video_alloc_buf[buf_offset];
    __this->video_cbuf_size = sizeof(video_alloc_buf) - buf_offset;
    printf("video buf size = %dB\n", __this->video_cbuf_size);

}

void video_calc_speed(int len)
{
    u32 tim = jiffies_to_msecs(get_jiffies());
    __this->data_len += len;

    if (__this->time_stamp_overflow) {
        if (tim >= __this->time_stamp) {
            return;
        } else {
            __this->time_stamp_overflow = 0;
        }
    }

    if (time_after(tim, __this->time_stamp + VIDEO_SPEED_CALC_TIM - 1)) {
        u32 video_speed = __this->data_len / 128  * 1000 / (tim - __this->time_stamp);
        __this->speed = __this->speed * VIDEO_SPEED_CALC_FLITER / 100 + video_speed * (100 - VIDEO_SPEED_CALC_FLITER) / 100;
        if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
            __this->time_stamp_overflow = 1;
        }

        __this->time_stamp = tim;
        __this->data_len = 0;
    }
}

u32 video_get_speed()
{
    return __this->speed;
}


void set_isp_debug_state(u8 state)
{
    __this->isp_debug = state;
}

int __attribute__((weak)) set_camera_freq(u32 frame_freq)
{
    return 0;
}

int video_put_buf(void *buf, int len, u32 arg);
int video_open(const char *camera_name, int idx, int fmt, int frame_id, int fps, u16 width, u16 height)
{
    struct camera_device_info camera_info = {0};
    struct jpeg_attr jattr = {0};

    eva_clk_init();
    eva_xbus_init();
    while (__this->frame_state == FRAME_GAP_STATE);
    JL_JPG->CON2 |= BIT(4); //line done 在kstart后才开始计数

    video_close();


    __this->camera = NULL;
    __this->imc_width = width;
    __this->imc_height = height;

    __this->data_len = 0;
    __this->time_stamp = jiffies_to_msecs(get_jiffies());
    if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
        __this->time_stamp_overflow = 1;
    }

    // set_board_ldo_power_init(); //开启电源

    /** camera_open **/
#ifdef ISP0_EN
    const char *cam_name = get_board_camera_name();
    /* __this->camera = dev_open(cam_name, 0); */
    __this->camera = dev_open(cam_name, (void *)&camera0_data);
#else
    const char *cam_name = "camera1";
    /* const char *cam_name = "cam1"; */
    __this->camera = dev_open(cam_name, 0);
#endif
    if (!__this->camera) {
        puts(">>> video open faild!\n");
        return 0;
    }
    if (cam_name[strlen(cam_name) - 1] == '0') {
        //isp0
        __this->sub_isp = sub_isp_open();
        if (__this->cur_fps != fps) {
            set_camera_freq(fps);
            __this->cur_fps = fps;
        }
    }

    dev_ioctl(__this->camera, CAMERA_GET_SENSOR_INFO, (u32)&camera_info);

 ////   printf("ccamera handle:%x, %d,%d\n", __this->camera, camera_info.width, camera_info.height);
 ////   printf("vvideo open idx:%d, fmt:%d, frame_id:%d fps:%d tar:%d x %d\n", idx, fmt, frame_id, fps, width, height);
    if (cam_name[strlen(cam_name) - 1] == '0') {
        // isp0
        // load isp config
        if (!__this->isp_debug) {
            struct isp_generic_cfg cfg;
            struct ispt_customize_cfg cus_cfg;

            cfg.id = ISP_GET_LV;
            int err = dev_ioctl((void *)__this->camera, ISP_IOCTL_GET_GENERIC_CFG, (unsigned int)&cfg);
            load_default_camera_config(cfg.lv, (void *)&cus_cfg);
            if (!err) {
                dev_ioctl((void *)__this->camera, ISP_IOCTL_SET_CUSTOMIZE_CFG, (unsigned int)&cus_cfg);
                start_update_isp_scenes((void *)__this->camera);
            }
        }
    }

    alloc_video_buf(fmt, width, height);
    u8 *out_buffer = video_yuv_buf;

    __this->video_buf = video_buf;
    cbuf_init(&__this->video_cbuf, __this->video_buf, __this->video_cbuf_size);

    /** imc open **/
    struct imc_iattr attr = {0};
    if (cam_name[strlen(cam_name) - 1] == '0') {
        attr.src = IMC_SRC_ISP0;
    } else {
        attr.src = IMC_SRC_ISP1;
    }
    attr.src_w = camera_info.width;
    attr.src_h = camera_info.height;
    attr.tar_w = width;
    attr.tar_h = (height == 1080) ? 1088 : height;
    if (fmt) {
        attr.tar_h = height;
    }
#ifdef CONFIG_USE_YUV420
    attr.yuvformat = IMC_YUV420;
#else
    attr.yuvformat = IMC_YUV422;
#endif

    attr.imc_buf_mode = IMC_ROUND_BUFF;
    attr.imc_int_cfg = IMC_16LINE;
    attr.crop_x = 0;
    attr.crop_y = 0;
    attr.crop_width = attr.src_w;
    attr.crop_height = attr.src_h;
    // get sensor name
    void *isp_sen;
    dev_ioctl(get_video_device(), CAMERA_GET_SENSOR_HANDLE, (u32)&isp_sen);
    isp_sen_t *c = (isp_sen_t *)isp_sen;
    if (camera_info.width == 1280 && camera_info.height == 720) {
        // C2595 1280p->720p 代码进入了这个分支这一行，但是之后没有进入任何一个分支
        if (strncmp("1A4T", (char *)c->logo, strlen((char *)c->logo)) == 0) {
            int crop_x_l = 150; // YU7 SC1346 lense shading是均匀的，对称裁剪
            int crop_x_r = 150;
            int dst_w = camera_info.width - (crop_x_l + crop_x_r);
            int dst_h = (int)(height / (float)width * dst_w);
            int crop_y = (camera_info.height - dst_h) / 2;
            if (crop_y <= 0)
                crop_y = 0;
            attr.crop_x = crop_x_l;
            attr.crop_y = crop_y;
            attr.crop_width = attr.src_w-(crop_x_l + crop_x_r);
            attr.crop_height = attr.src_h-2*crop_y;
            printf("crop_x:%d crop_y:%d width:%d height:%d\n",
                attr.crop_x, attr.crop_y,
                attr.crop_width, attr.crop_height);
        }
        else if (width == 640 && height == 480) {
            //720p->vga,需要剪裁
            attr.crop_x = 160;
            attr.crop_y = 0;
            attr.crop_width = attr.src_w;
            attr.crop_height = attr.src_h;
        }
    }
    else if (camera_info.width == 640 && camera_info.height == 480) {
        if (width == 864 && height == 480) {
            int crop_x = 0;
            int dst_w = camera_info.width - 2 * crop_x;
            int dst_h = (int)(height / (float)width * dst_w);
            int crop_y = (camera_info.height - dst_h) / 2;
            if (crop_y <= 0)
                crop_y = 0;
            attr.crop_x = crop_x;
            attr.crop_y = crop_y;
            attr.crop_width = attr.src_w-2 * crop_x;
            attr.crop_height = attr.src_h-2*crop_y;
        }
    }
    attr.dma_cnt = LINE_N;
    attr.out_buf = out_buffer;
    attr.mirror = 0; //水平镜像
    if (fmt == 1) {
        //yuv格式输出,imc配置yuv422
#ifdef CONFIG_USE_YUV420
        attr.yuvformat = IMC_YUV420;
#else
        attr.yuvformat = IMC_YUV422;
#endif
    }

    void *imc = imc_open(&attr);
    /* void *imc = ex_imc_open(&attr); */
    if (!imc) {
        puts(">>> imc open faild!\n");
        return 0;
    }
    __this->imc = imc;
    /* printf("imc==="); */
    /* printf_buf(imc, 64); */
    //osd config
    /* imc_osd_open(32,32); */
    if (fmt == 0) {
        //jpeg config
        jattr.jpeg_clock = 192; //96M
#ifdef FPGA
        jattr.jpeg_clock = 96; //96M
#endif

        jattr.width = width;
        jattr.height = (height == 1080) ? 1088 : height;
        jattr.enc_line = LINE_N;
        jattr.yuv_fmt = attr.yuvformat;
        jattr.fps = fps;
        jattr.in_buffer = out_buffer;
#ifdef CONFIG_YUYV_ENABLE
        jattr.bits_buffer = video_yuv_dest_buf;
        /* memset(video_yuv_dest_buf, 0, sizeof(video_yuv_dest_buf)); */
#else
        jattr.bits_buffer = jpeg_bit_buf;
#endif
        jattr.bits_size = JPEG_BIT_SIZE;
#ifdef HUSB_MODE   //USB2.0
        jattr.tar_Kbps = 20000;
        jattr.usb_mode = 0;
#else
        jattr.tar_Kbps = 5000;
        jattr.usb_mode = 1;
#endif
        jattr.set_fps_en = 1; //动态帧率设置开关
        __this->jpeg_set_fps_en = jattr.set_fps_en;
        jattr.abr_en = 1;
        jattr.abr_mode = 1;
        jattr.dri_en = 0; // 去除dri，据移康彭工描述，VM0没有这个限制，但是bk的都需要关闭dri
        jattr.jpeg_dri_mcu_cnt = 4; //jpeg分段位流mcu配置
        jattr.jpeg_output_buf = video_put_buf;
        jattr.jpeg_get_usb_speed = video_get_speed;
        jattr.jpeg_set_fps = NULL;
#ifdef HUSB_MODE   //USB2.0

        __this->speed = 60000;
#else
        __this->speed = 4000;//jattr.tar_Kbps;
#endif

#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
        int jpeg_ex_encoder_start(struct jpeg_attr * jattr);
        int jpeg_fd = jpeg_ex_encoder_start(&jattr);
        printf("jpeg start : %x\n", jpeg_fd);
#else
        //使用maskrom jpeg驱动
        jpeg_encoder_start(&jattr);
#endif
    } else {
        //yuv recorder config
        struct yuv_recorder_attr yuv_attr = {0};
        u8 *y = out_buffer;
        u8 *u = out_buffer + width * LINE_N;
        u8 *v = u + width * LINE_N / 2;
        yuv_attr.src_line_num = LINE_N;
        yuv_attr.y_start_addr = y;
        yuv_attr.u_start_addr = u;
        yuv_attr.v_start_addr = v;
        yuv_attr.image_width = width;
        yuv_attr.image_height = height;
        yuv_attr.des_line_num = (width > 640) ? 1 : YUV_LINE_N;
        yuv_attr.src_y_stride = width;
        yuv_attr.src_u_stride = width / 2;
        yuv_attr.src_v_stride = width / 2;
        yuv_attr.des_yuv_stride = width * 2;
#ifdef CONFIG_YUYV_ENABLE
        yuv_attr.des_start_addr = video_yuv_dest_buf;
#endif
        yuv_attr.yuv_output_buf = video_put_buf;
        imc_ch0_dma_con &= ~BIT(12); //取消imc 16对齐
        yuyv_recorder_open(&yuv_attr);

        yuyv_recorder_start();
    }
    __this->format = fmt;
    //imc kick start
    imc_kstart();

    __this->frame_end_byte = 0;
    /* __this->one_frame = 0; */
    __this->frame_state = 0;
    static u8 first_open = 0;
    if (first_open == 0) {
        __this->delay_out_frames = 0; /* 第一次开摄像头收敛慢,丢掉前面几帧 */
        first_open = 1;
    }

    return 0;

}

void video_close()
{
    if (__this->camera) {
        // disable pa power
        {
            extern void audio_spk_close_cb();
            audio_spk_close_cb();
        }
        puts("video close\n");
        stop_update_isp_scenes();
        dev_close(__this->camera);
        __this->camera = NULL;

        __this->speed = 0;
        __this->data_len = 0;
        __this->time_stamp = 0;
        __this->time_stamp_overflow = 0;
        __this->frame_state = 0;

        if (__this->sub_isp) {
#ifdef ISP0_EN
            sub_isp_close();
#endif
            __this->sub_isp = 0;
        }
#ifdef CONFIG_YUYV_ENABLE
        if (__this->format) {
            yuyv_recorder_stop();;
        }
#endif

        imc_close();

        if (__this->format == 0) {
#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
            int jpeg_ex_encoder_stop(void);
            jpeg_ex_encoder_stop();
#else
            // jpeg close
            while (JL_JPG->CON2 & BIT(11)); //等待jpeg总线空闲
            JL_JPG->CON0 = 0;
#endif
        }
        __this->format = 0;

    }
}


int video_put_buf(void *buf, int len, u32 arg)
{
    int wlen;
    int data_len = 0;
    /* if( __this->one_frame == 1){ */
    /* return 0; */
    /* } */
    /* printf("len:%d end byte=%d \n", len,__this->frame_end_byte); */
    if (__this->format) {
        //yuv
        __this->video_cbuf_size = len;
        __this->frame_end_byte = arg;
        return 0;
    }

    wlen = cbuf_write(&__this->video_cbuf, buf, len);
    if (wlen != len) {
        putchar('M');
        return -1;
    }
    if (arg) {
        data_len = cbuf_get_data_size(&__this->video_cbuf);
        if (!__this->frame_end_byte) {
            __this->frame_end_byte = data_len;
        }
        /* __this->one_frame++; */

        /* printf("end=%d \n", __this->frame_end_byte); */
    }

    return wlen;
}


int video_get_buf(void *buf, int len, u32 *arg)
{
    int rlen;
    int need_len = len;
    /* if( __this->one_frame == 1){ */
    /* return 0; */
    /* } */
    if (!__this->camera) {
        return 0;
    }
    video_calc_speed(need_len);

    if (__this->format) {
#ifdef CONFIG_YUYV_ENABLE
        //yuyv
        static u16 _offset;
        if (__this->video_cbuf_size) {

            len = (__this->video_cbuf_size < len) ? __this->video_cbuf_size : len;
            dma_memcpy(buf, video_yuv_dest_buf + _offset, len);
            __this->video_cbuf_size -= len;
            _offset += len;
            if (__this->video_cbuf_size <= 0) {
                _offset = 0;
                yuyv_recorder_start();
                if (__this->frame_end_byte) {
                    *arg = 1;
                    __this->frame_end_byte = 0;
                }
            }
            return len;
        }

        _offset = 0;
        return 0;
#endif
    }

    int data_len = cbuf_get_data_size(&__this->video_cbuf);
    len = (data_len < len) ? data_len : len;
    if (__this->format == 0) { //mjpeg
        if (__this->frame_end_byte) {
            len = (__this->frame_end_byte < len) ? __this->frame_end_byte : len;
            __this->frame_end_byte -= len ;
            if (__this->frame_end_byte <= 0) {
                *arg = 1;
                if (__this->delay_out_frames) {
                    __this->delay_out_frames--;
                }
                /* __this->one_frame++; */
            }
        }
        __this->frame_state = *arg ? FRAME_END_STATE : FRAME_GAP_STATE;
    }

    /* printf("\nrlen=%d %d\n", len,*arg); */
    rlen = cbuf_read(&__this->video_cbuf, buf, len);
    if (rlen != len) {
        printf("video buf read error! %d\n", rlen);
    }
    if (__this->format == 0 && __this->delay_out_frames) {
        memset(buf, 0, len);
    }
    /* if(len){ */
    /* if(*arg) { */
    /* printf("frame end:"); */
    /* printf_buf(&buf[rlen-2],2); */
    /* } */
    /* else { */
    /* printf_buf(buf,len); */
    /* __this->one_frame ++ ; */
    /* if(__this->one_frame == 4) { */
    /* printf_buf(buf,len); */

    /* } */

    /* } */
    /* } */

    return len;
}
int video_get_buf2(void **buf, int len, u32 *arg)
{
    printf("\npc cam case [ debug ]--func=%s line=%d\n", __func__, __LINE__);
    return 0;
}
void uvc_bulk_send()
{
}
void *get_video_device()
{
    return (void *)__this->camera;
}

/** imc crop interface **/
struct imc_crop_intput {
    u16 x;
    u16 y;
    u16 width;
    u16 height;
};

static u8 update_imc_crop = 0;
struct imc_crop_intput _imc_crop;
SET_INTERRUPT
static void __imc_isr_handler()
{
    struct imc_ch_reg *reg = (struct imc_ch_reg *)__this->imc;
    if (reg->scale->com_con & BIT(9)) {
        /* putchar('L'); */
        reg->scale->com_con |= BIT(8);
    }
    if (reg->scale->com_con & BIT(11)) {
        /* printf("frame done\n"); */
        /* putchar('F'); */
        reg->scale->com_con |= BIT(10);
        if (update_imc_crop) {

            update_imc_crop = 0;
            reg->scale->crop_h = (_imc_crop.x << 16) | (_imc_crop.x + _imc_crop.width);
            reg->scale->crop_v = (_imc_crop.y << 16) | (_imc_crop.y + _imc_crop.height);
            if (__this->imc_width > _imc_crop.width) {
                reg->scale->h_step = (_imc_crop.width - 1) * 256 / (__this->imc_width - 1);
            } else {
                reg->scale->h_step = _imc_crop.width * 256 / __this->imc_width;
            }
            if (__this->imc_height > _imc_crop.height) {
                reg->scale->v_step = (_imc_crop.height - 1) * 256 / (__this->imc_height - 1);
            } else {
                reg->scale->v_step = _imc_crop.height * 256 / __this->imc_height;
            }

            reg->scale->h_width =  __this->imc_width;
            reg->scale->v_width =  __this->imc_height;
        }


    }
    if (reg->scale->com_con & BIT(13)) {
        printf("imc bandwidth err\n");
        reg->scale->com_con |= BIT(12);
    }
    if (reg->scale->com_con & BIT(15)) {
        printf("imc line error\n");
        reg->scale->com_con |= BIT(14);
    }
    if (reg->scale->com_con & BIT(17)) {
        printf("imc osd error\n");
        reg->scale->com_con |= BIT(16);
    }

}

void imc_crop(u16 crop_x, u16 crop_y, u16 crop_width, u16 crop_height)
{
    struct imc_ch_reg *reg = (struct imc_ch_reg *)__this->imc;
    request_irq(IRQ_IMC_IDX, 0, __imc_isr_handler, 0);
    update_imc_crop = 1;

    _imc_crop.x = crop_x;
    _imc_crop.y = crop_y;
    _imc_crop.width = crop_width;
    _imc_crop.height = crop_height;
    if ((__this->imc_width / _imc_crop.width) >= 2) {
        _imc_crop.width = (__this->imc_width * 256) / ((2 - 1) * 256 + 255) + 1;
    }
    if ((__this->imc_height / _imc_crop.height) >= 2) {
        _imc_crop.height = (__this->imc_height * 256) / ((2 - 1) * 256 + 255) + 1;
    }

    printf("imc crop x:%d y:%d crop_w:%d crop_h:%d\n", _imc_crop.x, _imc_crop.y, _imc_crop.width, _imc_crop.height);
}

#endif
#endif
#endif
