import os
import re
import subprocess
import shutil
import sys 
from sync2minio import sync_to_minio

def get_script_path():
    return os.path.dirname(os.path.realpath(__file__))


def replace_config_values(config_path, replacements):
    try:
        print(f"正在处理的配置文件路径: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()

        for pattern, replacement in replacements.items():
            print(f"尝试匹配模式: {pattern}，替换为: {replacement}")
            original_content = content
            content = re.sub(pattern, replacement, content, flags=re.M)
            if original_content == content:
                print(f"警告：模式 {pattern} 未匹配到任何内容")
            else:
                print(f"替换后部分内容示例: {content[:200]}...")

        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("配置文件写入完成")

    except Exception as e:
        print(f"Error updating configuration file: {e}")
        import traceback
        traceback.print_exc()


def create_release_folder(release_folder_name):
    if not os.path.exists(release_folder_name):
        os.makedirs(release_folder_name)

def get_commit_id() -> str:
    process = subprocess.Popen(["git", "rev-parse", "--short", "HEAD"], shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    while True:
        output = process.stdout.readline()
        if output == '' and process.poll() is not None:
            break
        if output:
            return output.strip()
    if process.returncode != 0:
        print(f'get commit id fail')
        sys.exit(1)

def release_product(product_type, release_note = None):

    script_path = get_script_path()
    prj_root_path = script_path
    dot_cfg_path = os.path.join(prj_root_path, '.config')

    if not product_type:
        raise ValueError("product_type 不能为为空字符串")

    replacements = {
        # 将匹配到的配置项设置为 y
        fr'^(\s*)(#\s+)?{re.escape(product_type)}(\s*=.*| is not set)?$': rf'\1{product_type}=y',
        # 匹配其他以 CONFIG_PRODUCT 开头但不是目标产品的配置项，设置为 n
        fr'^(?!.*\b{re.escape(product_type)}\b)(\s*)(#\s+)?(CONFIG_PRODUCT\w+)(\s*=.*| is not set)?$': r'\1\3=n'
    }
    # Call the function to perform the replacement operation
    replace_config_values(dot_cfg_path, replacements)

    # run release script
    version_str = ''
    try:
        clean_process = subprocess.Popen(['make', 'clean'], shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        while True:
            output = clean_process.stdout.readline()
            if output == '' and clean_process.poll() is not None:
                break
            if output:
                print(output.strip())
        if clean_process.returncode != 0:
            raise subprocess.CalledProcessError(clean_process.returncode, 'make clean')

        make_process = subprocess.Popen(['make', '-j8'], shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        while True:
            output = make_process.stdout.readline()
            if output == '' and make_process.poll() is not None:
                break
            if output:
                print(output.strip())
        if make_process.returncode != 0:
            raise subprocess.CalledProcessError(make_process.returncode, 'make')

        get_version_process = subprocess.Popen(['make', 'get_fw_version'], shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        while True:
            output = get_version_process.stdout.readline()
            if output == '' and get_version_process.poll() is not None:
                break
            match_str = 'Version:'
            if match_str in output:
                version_str = output.split(match_str)[1].strip()
                print(f'version_str:{version_str}')
        if get_version_process.returncode != 0 or version_str == '':
            raise subprocess.CalledProcessError(get_version_process.returncode, 'get_version')

    except subprocess.CalledProcessError as e:
        print(f"{e.cmd} 命令执行失败")
        sys.exit(1)


    # create release folder
    # release_full_folder_name = os.path.join(script_path, f'YU7{major_ver}.{minor_ver}.{patch_ver}-{year}{month}{day}')
    release_full_folder_name = os.path.join(script_path,'build', version_str)
    create_release_folder(release_full_folder_name)

    firmware_path = os.path.join(prj_root_path, 'cpu', 'dv20','tools', 'jl_isd.fw')

    src_files = [firmware_path, dot_cfg_path]
    dst_files = [f'{version_str}_{get_commit_id()}.fw', 'dot_config']
    for index, file in enumerate(src_files):
        shutil.copy2(file, os.path.join(release_full_folder_name, dst_files[index]))

    # revert back .config
    subprocess.run(['git', 'checkout', dot_cfg_path], check=True)

    # sync to minio
    confirm = input("Sync Rleased SDK to minio ? (Y/N): ")
    if confirm.lower() not in ['y', 'yes']:
        return

    src_folder = os.path.join('build', version_str).replace(os.sep, "/")
    dst_folder = "FW_Release/S30_YU7_SU7"
    print(f"Syncing {src_folder} to minio {dst_folder}...")
    
    sync_to_minio(src_folder, dst_folder)

    release_note_path = os.path.join('release_note', release_note)
    if release_note is not None and os.path.exists(release_note_path):
        sync_to_minio(release_note_path.replace(os.sep, "/"), os.path.join(dst_folder).replace(os.sep, "/"))

    # add tag and push to origin
    subprocess.run(['git', 'tag', version_str], check=True)
    subprocess.run(['git', 'push', '--tags'], check=True)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--product', type = str, required=True)
    args = parser.parse_args()
    release_product(args)