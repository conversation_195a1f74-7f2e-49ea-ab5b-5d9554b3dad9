#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "ov02b.h"
#include "isp_alg.h"
#include "app_config.h"
#include "delay.h"
#include <math.h>

static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *ov02b_mipi_get_ae_params();
extern void *ov02b_mipi_get_awb_params();
extern void *ov02b_mipi_get_iq_params();
extern void ov02b_mipi_ae_ev_init(u32 fps);

// 从cis_ov02b.c移植的结构体定义（需要在函数声明之前）
typedef struct {
    float again;
    float dgain;
    float itime;
} cis_exposure_t;

typedef struct {
    float min_again, max_again, step_again;
    float min_dgain, max_dgain, step_dgain;
    float min_itime, max_itime, step_itime;
    float initial_again, initial_dgain, initial_itime;
} cis_exposure_param_t;

static void set_again(u32 gain);
static void set_dgain(u32 dgain);
static void set_shutter(u32 texp);

static int ov02b_get_exposure_param(cis_exposure_param_t *exp_param);
static int ov02b_mapping_itime(float itime, uint8_t *itime_h, uint8_t *itime_l);
static cis_exposure_t convert_exposure(float exposure, cis_exposure_param_t *exp_param);

/////////////////////////////////////////////////////////////////////////
// NOTE: these default ae parames will be override by value in facelock_cfg.json
#define DEFAULT_AGAIN            (8.0f)     // 8x
#define DEFAULT_DGAIN            (1.25f)     // 1.25x
#define DEFAULT_EXPOSURE         (1000)     // 1000 lines
/////////////////////////////////////////////////////////////////////////

#define MAX_ITIME_MS             (120)
#define DEFAULT_SLEEP_DELAY_MS   (80)       // this delay should be larger than itime

#define REG_DLY                  (0xff)
#define SENSOR_ADDR_WR           (0x78 >> 1)

#define LINE_LENGTH_CLK     448     // HTS
#define FRAME_LENGTH        486     // VTS

#define ROW_TIME_NS         27150  // TLine: 27.15us from txt
#define INPUT_CLK  24
#define PCLK  66

static u32 uframelen = FRAME_LENGTH;


static u32 cur_again = 0;
static u32 cur_dgain = 0;
static u32 cur_expline = 0;
static u32 debug_counter = 0;

static u32 line_length_clk = LINE_LENGTH_CLK;

static void *iic = NULL;

/////////////////// IIC 地址 //////////////////////
#define WRCMD 0x78  // 写地址
#define RDCMD 0x79  // 读地址
///////////////////////////////////////////////////

////////////////// check sensor ///////////////////
#define device_id_h 0x02
#define device_id_l 0x03
#define device_value_h 0x00
#define device_value_l 0x2B
///////////////////////////////////////////////////

//1280x720: mclk = 24M, mipi data rate = 660M bps, raw10, 30fps
static const sensor_reg_t ov02b_1280x720[] = {
    {0xfc, 0x01},   //soft reset
    {REG_DLY, 5},   //delay 5ms

    {0xfd, 0x00},
    {0x24, 0x02},   //pll_mc
    {0x25, 0x06},   //pll_nc,dpll clk 72M
    {0x29, 0x01},
    {0x2a, 0xb4},   //mpll_nc, mpll clk 660M
    {0x2b, 0x00},
    {0x1e, 0x17},   //vlow 0.53v
    {0x33, 0x07},   //ipx 2.84u
    {0x35, 0x07},
    {0x4a, 0x0c},   //ncp -1.4v
    {0x3a, 0x05},   //icomp1 4.25u
    {0x3b, 0x02},   //icomp2 1.18u
    {0x3e, 0x00},
    {0x46, 0x01},
    {0x6d, 0x03},
    {0xfd, 0x01},
    {0x0e, 0x02},
    {0x0f, 0x1a},   //exp
    {0x18, 0x00},   //un fixed-fps
    {0x22, 0xff},   //analog gain
    {0x23, 0x02},   //adc_range 0.595v
    {0x17, 0x2c},   //pd reset row address time
    {0x19, 0x20},   //dac_d0 1024
    {0x1b, 0x06},   //rst_num1 96
    {0x1c, 0x04},   //rst_num2 64
    {0x20, 0x03},
    {0x30, 0x01},   //p0
    {0x33, 0x01},   //p3
    {0x31, 0x0a},   //p1
    {0x32, 0x09},   //p2
    {0x38, 0x01},
    {0x39, 0x01},   //p9
    {0x3a, 0x01},   //p10
    {0x3b, 0x01},
    {0x4f, 0x04},   //p24
    {0x4e, 0x05},   //p23
    {0x50, 0x01},   //p25
    {0x35, 0x0c},   //p5
    {0x45, 0x2a},   //sc1,p20_1
    {0x46, 0x2a},   //p20_2
    {0x47, 0x2a},   //p20_3
    {0x48, 0x2a},   //p20_4
    {0x4a, 0x2c},   //sc2,p22_1
    {0x4b, 0x2c},   //p22_2
    {0x4c, 0x2c},   //p22_3
    {0x4d, 0x2c},   //p22_4
    {0x56, 0x3a},   //p31, 1st d0
    {0x57, 0x0a},   //p32, 1st d1
    {0x58, 0x24},   //col_en1
    {0x59, 0x20},   //p34 2nd d0
    {0x5a, 0x0a},   //p34 2nd d1
    {0x5b, 0xff},   //col_en2
    {0x37, 0x0a},   //p7, tx
    {0x42, 0x0e},   //p17, psw
    {0x68, 0x90},
    {0x69, 0xcd},   //blk en, no sig_clamp
    {0x6a, 0x8f},
    {0x7c, 0x0a},
    {0x7d, 0x09},	//0a
    {0x7e, 0x09},	//0a
    {0x7f, 0x08},
    {0x83, 0x14},
    {0x84, 0x14},
    {0x86, 0x14},
    {0x87, 0x07},   //vbl2_4
    {0x88, 0x0f},
    {0x94, 0x02},   //evsync del frame
    {0x98, 0xd1},   //del bad frame
    {0xfe, 0x02},
    {0xfd, 0x03},   //RegPage
    {0x97, 0x78},
    {0x98, 0x78},
    {0x99, 0x78},
    {0x9a, 0x78},
    {0xa1, 0x40},
    {0xb1, 0x30},
    {0xae, 0x0d},   //bit0=1,high 8bit
    {0x88, 0x5b},   //BLC_ABL
    {0x89, 0x7c},   //bit6=1 trigger en
    {0xb4, 0x05},   //mean trigger 5
    {0x8c, 0x40},   //BLC_BLUE_SUBOFFSET_8lsb
    {0x8e, 0x40},   //BLC_RED_SUBOFFSET_8lsb
    {0x90, 0x40},   //BLC_GR_SUBOFFSET_8lsb
    {0x92, 0x40},   // BLC_GB_SUBOFFSET_8lsb
    {0x9b, 0x46},   //digtal gain
    {0xac, 0x40},   //blc random noise rpc_th 4x
    {0xfd, 0x00},
    {0x5a, 0x15},
    {0x74, 0x01},   // //PD_MIPI:turn on mipi phy

    {0xfd, 0x00},  //crop to 1280x720
    {0x4f, 0x05},
    {0x50, 0x00},  //mipi hszie low 8bit
    {0x51, 0x02},
    {0x52, 0xd0},  //mipi vsize low 8bit

    {0xfd, 0x01},
    {0x02, 0x00},
    {0x03, 0xc0},  //window hstart low 8bit
    {0x04, 0x01},
    {0x05, 0x00},  //window vstart low 8bit
    {0x06, 0x02},
    {0x07, 0x80},  //window hsize low 8bit
    {0x08, 0x02},
    {0x09, 0xd0},  //window vsize low 8bit

    {0x14, 0x01},  // VTS高8位：恢复官方配置
    {0x15, 0xe6},  // VTS低8位：0x01e6 = 486行，官方30fps配置
    {0xfe, 0x02},

    {0xfb, 0x01},

    //stream on
    {0xfd, 0x03},
    {0xc2, 0x01},  //MIPI_EN
    {0xfd, 0x01},
};

// OV02B寄存器写入函数 - 简化版本，参考cis_ov02b.c
unsigned char wr_ov02b_reg(u8 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    // 发送写命令
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }

    // 发送寄存器地址
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }

    // 发送数据
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }

__wend:
    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// OV02B寄存器读取函数（参考GC1054的实现）
unsigned char rd_ov02b_reg(u8 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

/*************************************************************************************************
    sensor api
*************************************************************************************************/
// 配置传感器 输出分辨率 帧率
static u32 ov02b_frame_rate = 30;
void ov02b_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u8 v;

    printf("OV02B configuring %dx%d fps:%d\n", *width, (*height), *frame_freq);
    ov02b_mipi_set_output_size(width, height, frame_freq);

    // 使用OV02B传感器配置
    for (i = 0; i < sizeof(ov02b_1280x720) / sizeof(sensor_reg_t); i++) {
        // 处理延迟指令
        if (ov02b_1280x720[i].addr == REG_DLY) {
            delay_ms(ov02b_1280x720[i].val);
            continue;
        }

        if(!wr_ov02b_reg(ov02b_1280x720[i].addr, ov02b_1280x720[i].val)) {
            printf("写入寄存器失败：0x%02X=0x%02X\n", ov02b_1280x720[i].addr, ov02b_1280x720[i].val);
        }


    }

    ov02b_frame_rate = *frame_freq;
    printf("[OV02B] 配置完成，FPS设置为: %d fps\n", ov02b_frame_rate);

    ov02b_mipi_ae_ev_init(*frame_freq);
    cur_again = 0;
    cur_dgain = 0;
    cur_expline = 0;
    return;
}

s32 ov02b_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 ov02b_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

s32 ov02b_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    
    wr_ov02b_reg(0xfd, 0x00);  // 选择页面0
    
    for (int i = 0; i < 3; i++) {
        rd_ov02b_reg(device_id_h, &pid);
        rd_ov02b_reg(device_id_l, &ver);
        
        if (pid == device_value_h && ver == device_value_l) {
            break;
        }
        delay_ms(5);
    }
    
    puts("OV02B Sensor PID \n");
    put_u8hex(pid);
    put_u8hex(ver);
    puts("\n");
    
    if (pid != device_value_h || ver != device_value_l) {
        puts("----not OV02B sensor-----\n");
        return -1;
    }
    puts("----hello OV02B sensor-----\n");
    return 0;
}

void ov02b_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    gpio_direction_output(reset_gpio, 0);
    gpio_direction_output(pwdn_gpio, 0);
    delay_ms(5);
    gpio_direction_output(pwdn_gpio, 1);
    delay_ms(5);
    gpio_direction_output(reset_gpio, 1);
    delay_ms(5);

    return;
}

static u8 cur_sensor_type = 0xff;

s32 ov02b_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{
    puts("\n\n ov02b_mipi_check \n\n");

    printf("\n isp_dev OV02B==========%d\n", isp_dev);
    if (!iic) {
        iic = dev_open("iic0", &_hw_iic);
        /* iic = dev_open("swiic0", &_sw_iic); */
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
    printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    ov02b_mipi_reset(isp_dev);

    if (0 != ov02b_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

s32 ov02b_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\n OV02B_mipi_init \n\n");

    ov02b_mipi_reset(isp_dev);
    delay_ms(5);
    ov02b_mipi_config_SENSOR(width, height, format, frame_freq);
    delay_ms(5);
    
    printf("OV02B init complete, streaming enabled\n");
    return 0;
}

// 设置模拟增益（analog gain）
void set_again(u32 again)
{
    u8 gain_reg;
    u32 gain_calc;

    if (cur_again == again) {
        return;
    }

    cur_again = again;
    gain_calc = (again * 16) / 1024;

    if (gain_calc < 16) {
        gain_calc = 16;
    }
    if (gain_calc > 248) {
        gain_calc = 248;
    }

    gain_reg = (u8)gain_calc;

    wr_ov02b_reg(0xFD, 0x1);
    wr_ov02b_reg(0x22, gain_reg);
    // wr_ov02b_reg(0xFE, 0x02);
}

// 设置数字增益（digital gain）
static void set_dgain(u32 dgain)
{
    u8 dgain_reg;
    u32 dgain_calc;

    if (cur_dgain == dgain) {
        return;
    }

    cur_dgain = dgain;
    dgain_calc = (dgain * 64) / 100;

    if (dgain_calc < 64) {
        dgain_calc = 64;
    }
    if (dgain_calc > 255) {
        dgain_calc = 255;
    }

    dgain_reg = (u8)dgain_calc;

    wr_ov02b_reg(0xFD, 0x3);
    wr_ov02b_reg(0x9B, dgain_reg);
    // wr_ov02b_reg(0xFE, 0x02);
}


// 计算增益
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    // 将ISP传入的gain转换为OV02B的增益单位，倍数
    float total_gain = (float)gain / 1024.0f;

    cis_exposure_param_t exp_param;
    ov02b_get_exposure_param(&exp_param);

    cis_exposure_t cis_exp = convert_exposure(total_gain, &exp_param);

    *_again = (u32)(cis_exp.again * 1024);
    *_dgain = (u32)(cis_exp.dgain * 100);
}

// 设置曝光时间
static void set_shutter(u32 texp)
{
    u8 itime_h, itime_l;

    if (cur_expline == texp) {
        return;
    }

    cur_expline = texp;

    ov02b_mapping_itime((float)texp, &itime_h, &itime_l);

    wr_ov02b_reg(0xFD, 0x01);
    wr_ov02b_reg(0x0E, itime_h);
    wr_ov02b_reg(0x0F, itime_l);
}

// 计算曝光时间
u32 ov02b_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;
    u32 max_texp;

    // 根据OV02B的时序参数计算实际曝光行数
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;

    // gain > 7000 认为是暗光环境
    printf("gain = %d\n", gain);
    if (gain > 7000) {
        max_texp = 5000;
    } else {
        max_texp = 1200;
    }

    // 保证最小曝光行数
    if (texp < 100) {
        texp = 100;
    }
    else if (texp > max_texp) {
        texp = max_texp;
    }

    // 计算实际对应的曝光时间
    texp_align = (texp) * LINE_LENGTH_CLK / (PCLK * 1);

    // 增益补偿比例计算
    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << 10) / texp_align;
    }
    else {
        ratio = (1 << 10);
    }

    // 更新曝光参数
    shutter->ae_exp_line = texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;

    return 0;
}

// 设置曝光参数
u32 ov02b_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    u32 again, dgain;
    calc_gain((shutter->ae_gain), &again, &dgain);

    set_again(again);
    set_dgain(dgain);
    set_shutter(shutter->ae_exp_line);
    wr_ov02b_reg(0xFE, 0x02);

    // 计算实际曝光时间用于日志显示
    u32 exp_time_us = (shutter->ae_exp_line * LINE_LENGTH_CLK) / PCLK;

    // 读取寄存器中的实际值进行验证 - 需要切换到正确的页面
    u8 again_reg, dgain_reg, itime_high, itime_low;

    // 读取again寄存器 (page 1)
    wr_ov02b_reg(0xFD, 0x01);
    rd_ov02b_reg(0x22, &again_reg);

    // 读取dgain寄存器 (page 3)
    wr_ov02b_reg(0xFD, 0x03);
    rd_ov02b_reg(0x9B, &dgain_reg);

    // 读取itime寄存器 (page 1)
    wr_ov02b_reg(0xFD, 0x01);
    rd_ov02b_reg(0x0E, &itime_high);
    rd_ov02b_reg(0x0F, &itime_low);

    u16 itime_readback = (itime_high << 8) | itime_low;
    u32 again_readback = (again_reg * 1024) / 16;
    u32 dgain_readback = (dgain_reg * 100) / 64;

    printf("OV02B AE: gain=%d again=%d(dec) dgain=%d exp_lines=%d exp_time_us=%d \n 寄存器回读: again[%02X=%d] dgain[%02X=%d] itime[%02X,%02X=%d]\n",
           shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us,
           again_reg, again_readback, dgain_reg, dgain_readback,
           itime_high, itime_low, itime_readback);

    return 0;
}

void ov02b_mipi_sleep()
{
}

void ov02b_mipi_wakeup()
{
}

// 写寄存器接口
void ov02b_mipi_wr_reg(u16 addr, u16 val)
{
    wr_ov02b_reg(addr, (u8)val);
}

// 读寄存器接口
u16 ov02b_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_ov02b_reg(addr, &val);
    return val;
}

REGISTER_CAMERA(ov02b_mipi) = {
    .logo               = "ov02b",
    .isp_dev            = ISP_DEV_NONE,        // 使用ISP_DEV_NONE，让系统自动分配
    .in_format          = SEN_IN_FORMAT_BGGR,   // OV02B使用BGGR格式
    .out_format         = ISP_OUT_FORMAT_YUV,  // 使用YUV输出格式
    .mbus_type          = SEN_MBUS_CSI2,
    .mbus_config        = SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,  // 10位数据，1通道
    .fps                = 15,  // 默认30fps
    .sen_size           = {OV02B_MIPI_OUTPUT_W, OV02B_MIPI_OUTPUT_H},  // 1280*720
    .isp_size           = {OV02B_MIPI_OUTPUT_W, OV02B_MIPI_OUTPUT_H},
    .cap_fps            = 15,
    .sen_cap_size       = {OV02B_MIPI_OUTPUT_W, OV02B_MIPI_OUTPUT_H},
    .isp_cap_size       = {OV02B_MIPI_OUTPUT_W, OV02B_MIPI_OUTPUT_H},

    .ops                = {
        .avin_fps           = NULL,
        .avin_valid_signal  = NULL,
        .avin_mode_det      = NULL,
        .sensor_check       = ov02b_mipi_check,
        .init               = ov02b_mipi_init,
        .set_size_fps       = ov02b_mipi_set_output_size,
        .power_ctrl         = ov02b_mipi_power_ctl,

        .get_ae_params      = ov02b_mipi_get_ae_params,
        .get_awb_params     = ov02b_mipi_get_awb_params,
        .get_iq_params      = ov02b_mipi_get_iq_params,

        .sleep              = ov02b_mipi_sleep,
        .wakeup             = ov02b_mipi_wakeup,
        .write_reg          = ov02b_mipi_wr_reg,
        .read_reg           = ov02b_mipi_rd_reg,
    }
};

// 获取曝光参数范围
static int ov02b_get_exposure_param(cis_exposure_param_t *exp_param)
{
    exp_param->min_again        = 1.0;
    exp_param->max_again        = 15.5;
    exp_param->step_again       = 0.1;
    exp_param->min_dgain        = 1.0;
    exp_param->max_dgain        = 4.0;
    exp_param->step_dgain       = 0.5;
    exp_param->min_itime        = 1;
    exp_param->max_itime        = 1000;
    exp_param->step_itime       = 1;
    exp_param->initial_again    = DEFAULT_AGAIN;
    exp_param->initial_dgain    = DEFAULT_DGAIN;
    exp_param->initial_itime    = DEFAULT_EXPOSURE;

    return 0;
}

// 曝光时间转换函数
static int ov02b_mapping_itime(float itime, uint8_t *itime_h, uint8_t *itime_l)
{
    int itime_i = 0;

    if (itime > 1.0) {
        // for itime > 1.0, we suppose it is "exposure lines"
        itime_i = itime;
    } else {
        // for itime <= 1.0 we suppose it is "exposure time"
        float mipi_data_rate = 660 * 1000000;
        float mipi_lane_num  = 1;                   // 1 lane
        float mipi_data_bit  = 10;                  // raw 10
        float pclk           = mipi_data_rate * mipi_lane_num / mipi_data_bit;
        float T_pclk         = 1/pclk;
        float T_line         = 0x1c0 * T_pclk * 4;
        int   itime_lines    = (int)round(itime / T_line);
        itime_i              = itime_lines;
    }
    *itime_h = (itime_i >> 8) & 0xff;
    *itime_l = itime_i & 0xff;
    return 0;
}

// 曝光参数分配函数
static cis_exposure_t convert_exposure(float exposure, cis_exposure_param_t *exp_param)
{
    float new_itime = 1.0;
    float new_again = 1.0;
    float new_dgain = 1.0;

    float max_again = exp_param->max_again;  // 15.5
    float max_dgain = exp_param->max_dgain;  // 4.0
    float max_itime_for_motion = 4.0;
    float remaining_exposure = exposure;

    // itime
    if (remaining_exposure > max_itime_for_motion) {
        new_itime = max_itime_for_motion;
        remaining_exposure = remaining_exposure / max_itime_for_motion;
    } else {
        new_itime = remaining_exposure;
        remaining_exposure = 1.0;
    }

    // again
    if (remaining_exposure > 1.0) {
        if (remaining_exposure > max_again) {
            new_again = max_again;
            remaining_exposure = remaining_exposure / max_again;
        } else {
            // 不要将remaining_exposure重置为1.0 保留一部分给dgain使用
            if (remaining_exposure > 2.0) {
                // 如果剩余曝光>2倍，again用一半，dgain用一半
                float again_part = sqrt(remaining_exposure);
                new_again = again_part;
                remaining_exposure = remaining_exposure / again_part;
            } else {
                new_again = remaining_exposure;
                remaining_exposure = 1.0;
            }
        }
    }

    // dgain
    if (remaining_exposure > 1.0) {
        if (remaining_exposure > max_dgain) {
            new_dgain = max_dgain;
            // 如果还有剩余，只能接受欠曝
        } else {
            new_dgain = remaining_exposure;
        }
    }

    cis_exposure_t cis_exp = {
        .again = new_again,
        .dgain = new_dgain,
        .itime = new_itime,
    };

    return cis_exp;
}