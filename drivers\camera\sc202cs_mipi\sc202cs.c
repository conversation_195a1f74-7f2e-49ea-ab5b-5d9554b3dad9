#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "sc202cs.h"
#include "isp_alg.h"
#include "app_config.h"
#include "delay.h"

// 硬件参数定义
#define LINE_LENGTH_CLK       1600    // 行长度时钟数（HTS），影响行时间
#define FRAME_LENGTH          1200    // 帧长度行数（VTS），影响帧率
#define ROW_TIME_NS           66667   // 单行时间（纳秒），用于时序计算
#define INPUT_CLK             24      // 输入时钟频率（MHz）
#define PCLK                  90      // 像素时钟频率（MHz）

// I2C通信定义
#define WRCMD        0x6C    // I2C写地址
#define RDCMD        0x6D    // I2C读地址

// 传感器ID定义
#define device_id_h       0x3107  // 芯片ID高字节寄存器地址
#define device_id_l       0x3108  // 芯片ID低字节寄存器地址
#define device_value_h    0xEB    // 芯片ID高字节期望值
#define device_value_l    0x52    // 芯片ID低字节期望值

// 寄存器地址定义
#define SC202CS_EXPOSURE_HIGH_REG     0x0202  // 曝光时间高字节寄存器
#define SC202CS_EXPOSURE_LOW_REG      0x0203  // 曝光时间低字节寄存器
#define SC202CS_DGAIN_REG             0x3E06  // 数字增益高字节寄存器
// #define SC202CS_DGAIN_LOW_REG         0x0217  // 数字增益低字节寄存器
#define SC202CS_VTS_HIGH_REG          0x0340  // 垂直总行数高字节寄存器
#define SC202CS_VTS_LOW_REG           0x0341  // 垂直总行数低字节寄存器
#define SC202CS_HTS_HIGH_REG          0x0342  // 水平总像素数高字节寄存器
#define SC202CS_HTS_LOW_REG           0x0343  // 水平总像素数低字节寄存器
#define SC202CS_STREAM_CTRL_REG       0x0100  // 流控制寄存器
#define SC202CS_STREAM_ON             0x01    // 开启视频流输出
#define SC202CS_STREAM_OFF            0x00    // 关闭视频流输出

// 模拟增益寄存器组
#define SC202CS_AGAIN_REG_3E09        0x3E09  // 模拟增益寄存器
#define SC202CS_DGAIN_FINE_REG        0x3E07  // 数字增益精调寄存器
#define SC202CS_AGC_CTRL_REG          0x3E03  // AGC控制寄存器

// 参数限制定义
#define SC202CS_MIN_EXPOSURE_LINES    150     // 最小曝光行数限制
#define SC202CS_VTS_MARGIN            8       // VTS安全边距（行数）
#define SC202CS_MAX_VTS               5000    // 最大VTS值限制
#define SC202CS_AGAIN_MIN             1024    // 最小模拟增益值（1x）
#define SC202CS_AGAIN_MAX             16384   // 最大模拟增益值（16x）
#define SC202CS_DGAIN_MIN             1024    // 最小数字增益值（1x）
#define SC202CS_DGAIN_MAX             4032    // 最大数字增益值（3.938x）
#define RATIO_SHIFT_BITS            10      // 比例计算位移位数
#define GAIN_CONVERT_BASE           100     // 增益转换基数
#define GAIN_CONVERT_FACTOR         1024    // 增益转换系数
#define SC202CS_I2C_DELAY_US        10      // I2C通信延时（微秒）
#define SC202CS_FPS_30              30      // 30fps帧率标识
#define SC202CS_FPS_15              15      // 15fps帧率标识
// 时序参数定义
#define SC202CS_VTS_30FPS           1232    // 30fps对应的VTS值
#define SC202CS_VTS_15FPS           2464    // 15fps对应的VTS值
#define SC202CS_HTS_DEFAULT         2432    // 默认HTS值

#define INVALID_GPIO_PIN            -1  // 无效GPIO标识

// 调试开关
#define DEBUG_AE 0

// 全局变量定义
static u32 reset_gpios[2] = {INVALID_GPIO_PIN, INVALID_GPIO_PIN};    // 复位GPIO数组
static u32 pwdn_gpios[2] = {INVALID_GPIO_PIN, INVALID_GPIO_PIN};     // 掉电GPIO数组
static u32 cur_again = INVALID_GPIO_PIN;                             // 当前模拟增益缓存
static u32 cur_dgain = INVALID_GPIO_PIN;                             // 当前数字增益缓存
static u32 cur_expline = INVALID_GPIO_PIN;                           // 当前曝光行数缓存
static u32 cur_line_length_clk = INVALID_GPIO_PIN;                   // 当前行长度时钟缓存
static u32 cur_vts = 2464;                                           // 当前VTS值（初始15fps）
static u32 line_length_clk = LINE_LENGTH_CLK;                  // 行长度时钟值
static void *iic = NULL;  // I2C设备句柄

// 外部函数声明
extern void *sc202cs_mipi_get_ae_params();
extern void *sc202cs_mipi_get_awb_params();
extern void *sc202cs_mipi_get_iq_params();
extern void sc202cs_mipi_ae_ev_init(u32 fps);

// 内部函数声明
static void set_again(u32 again);    // 设置模拟增益
static void set_dgain(u32 dgain);    // 设置数字增益
static void set_shutter(u32 texp);   // 设置曝光时间
static void set_vts(u32 vts);        // 设置VTS

// 1280x720分辨率寄存器配置表
static const sensor_reg_t sc202cs_1280x720_30fps[] = {
    {0x0103,0x01},
    {0x0100,0x00},
    {0x36e9,0x80},
    {0x36e9,0x24},
    {0x301f,0x07},
    {0x3200,0x00},
    {0x3201,0xa0},
    {0x3202,0x00},
    {0x3203,0xf0},
    {0x3204,0x05},
    {0x3205,0xa7},
    {0x3206,0x03},
    {0x3207,0xc7},
    {0x3208,0x05},
    {0x3209,0x00},
    {0x320a,0x02},
    {0x320b,0xd0},
    {0x3210,0x00},
    {0x3211,0x04},
    {0x3212,0x00},
    {0x3213,0x04},
    {0x3301,0xff},
    {0x3304,0x68},
    {0x3306,0x40},
    {0x3308,0x08},
    {0x3309,0xa8},
    {0x330b,0xb0},
    {0x330c,0x18},
    {0x330d,0xff},
    {0x330e,0x20},
    {0x331e,0x59},
    {0x331f,0x99},
    {0x3333,0x10},
    {0x335e,0x06},
    {0x335f,0x08},
    {0x3364,0x1f},
    {0x337c,0x02},
    {0x337d,0x0a},
    {0x338f,0xa0},
    {0x3390,0x01},
    {0x3391,0x03},
    {0x3392,0x1f},
    {0x3393,0xff},
    {0x3394,0xff},
    {0x3395,0xff},
    {0x33a2,0x04},
    {0x33ad,0x0c},
    {0x33b1,0x20},
    {0x33b3,0x38},
    {0x33f9,0x40},
    {0x33fb,0x48},
    {0x33fc,0x0f},
    {0x33fd,0x1f},
    {0x349f,0x03},
    {0x34a6,0x03},
    {0x34a7,0x1f},
    {0x34a8,0x38},
    {0x34a9,0x30},
    {0x34ab,0xb0},
    {0x34ad,0xb0},
    {0x34f8,0x1f},
    {0x34f9,0x20},
    {0x3630,0xa0},
    {0x3631,0x92},
    {0x3632,0x64},
    {0x3633,0x43},
    {0x3637,0x49},
    {0x363a,0x85},
    {0x363c,0x0f},
    {0x3650,0x31},
    {0x3670,0x0d},
    {0x3674,0xc0},
    {0x3675,0xa0},
    {0x3676,0xa0},
    {0x3677,0x92},
    {0x3678,0x96},
    {0x3679,0x9a},
    {0x367c,0x03},
    {0x367d,0x0f},
    {0x367e,0x01},
    {0x367f,0x0f},
    {0x3698,0x83},
    {0x3699,0x86},
    {0x369a,0x8c},
    {0x369b,0x94},
    {0x36a2,0x01},
    {0x36a3,0x03},
    {0x36a4,0x07},
    {0x36ae,0x0f},
    {0x36af,0x1f},
    {0x36bd,0x22},
    {0x36be,0x22},
    {0x36bf,0x22},
    {0x36d0,0x01},
    {0x370f,0x02},
    {0x3721,0x6c},
    {0x3722,0x8d},
    {0x3725,0xc5},
    {0x3727,0x14},
    {0x3728,0x04},
    {0x37b7,0x04},
    {0x37b8,0x04},
    {0x37b9,0x06},
    {0x37bd,0x07},
    {0x37be,0x0f},
    {0x3901,0x02},
    {0x3903,0x40},
    {0x3905,0x8d},
    {0x3907,0x00},
    {0x3908,0x41},
    {0x391f,0x41},
    {0x3933,0x80},
    {0x3934,0x02},
    {0x3937,0x6f},
    {0x393a,0x01},
    {0x393d,0x01},
    {0x393e,0xc0},
    {0x39dd,0x41},
    {0x3e00,0x00},
    {0x3e01,0x4d},
    {0x3e02,0xc0},
    {0x3e09,0x00},
    {0x4509,0x28},
    {0x450d,0x61},
    {0x0100,0x01},
};

// 写入SC202CS 8位地址寄存器
unsigned char wr_sc202cs_reg_8bit(u8 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 16位地址寄存器写入函数
unsigned char wr_sc202cs_reg(u16 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID & 0xff)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 8位地址寄存器读取函数
unsigned char rd_sc202cs_reg_8bit(u8 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(SC202CS_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
        ret = 0;
        goto __rend;
    }

    delay(SC202CS_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(SC202CS_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

// 16位地址寄存器读取函数
unsigned char rd_sc202cs_reg(u16 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __rend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID & 0xff)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

// sensor api
static u32 sc202cs_frame_rate = SC202CS_FPS_30;  // 当前帧率设置
void sc202cs_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;

    sc202cs_mipi_set_output_size(width, height, frame_freq);

    // 写入基础寄存器配置表（1280x720分辨率配置）
    for (i = 0; i < sizeof(sc202cs_1280x720_30fps) / sizeof(sensor_reg_t); i++) {
        wr_sc202cs_reg(sc202cs_1280x720_30fps[i].addr, sc202cs_1280x720_30fps[i].val);
    }
    delay_ms(10);

    // 开启AGC寄存器控制 - 根据数据手册要求
    wr_sc202cs_reg(SC202CS_AGC_CTRL_REG, 0x0b);
    // 根据帧率设置VTS和HTS时序参数
    // sc202cs_frame_rate = *frame_freq;
    // if (sc202cs_frame_rate == SC202CS_FPS_30) {
    //     // 30fps时序配置
    //     wr_sc202cs_reg(SC202CS_VTS_HIGH_REG, (SC202CS_VTS_30FPS >> 8) & 0xFF);
    //     wr_sc202cs_reg(SC202CS_VTS_LOW_REG, SC202CS_VTS_30FPS & 0xFF);
    //     wr_sc202cs_reg(SC202CS_HTS_HIGH_REG, (SC202CS_HTS_DEFAULT >> 8) & 0xFF);
    //     wr_sc202cs_reg(SC202CS_HTS_LOW_REG, SC202CS_HTS_DEFAULT & 0xFF);
    // } else if (sc202cs_frame_rate == SC202CS_FPS_15) {
    //     // 15fps时序配置
    //     wr_sc202cs_reg(SC202CS_VTS_HIGH_REG, (SC202CS_VTS_15FPS >> 8) & 0xFF);
    //     wr_sc202cs_reg(SC202CS_VTS_LOW_REG, SC202CS_VTS_15FPS & 0xFF);
    //     wr_sc202cs_reg(SC202CS_HTS_HIGH_REG, (SC202CS_HTS_DEFAULT >> 8) & 0xFF);
    //     wr_sc202cs_reg(SC202CS_HTS_LOW_REG, SC202CS_HTS_DEFAULT & 0xFF);
    // }

    // 初始化AE参数表
    sc202cs_mipi_ae_ev_init(*frame_freq);

    // 重置当前参数缓存
    cur_again = INVALID_GPIO_PIN;
    cur_dgain = INVALID_GPIO_PIN;
    cur_expline = INVALID_GPIO_PIN;
    cur_line_length_clk = INVALID_GPIO_PIN;

    return;
}

// 设置输出尺寸
s32 sc202cs_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}

// 电源控制
s32 sc202cs_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

// 检查C2595传感器ID
s32 sc202cs_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i;

    wr_sc202cs_reg(device_id_h, 0x00);
    for (i = 0; i < 3; i++) {
        rd_sc202cs_reg(device_id_h, &pid);
        rd_sc202cs_reg(device_id_l, &ver);
    }

    printf("sc202cs Sensor PID \n");
    put_u8hex(pid);
    put_u8hex(ver);
    printf("\n");

    if (pid != device_value_h || ver != device_value_l) {
        printf("----not sc202cs sensor-----\n");
        return -1;
    }
    printf("----hello sc202cs sensor-----\n");
    return 0;
}

// sc202cs传感器复位
void sc202cs_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    gpio_direction_output(pwdn_gpio, 1);
    delay_ms(4);
}

static u8 cur_sensor_type = 0xFF;  // 当前传感器类型
// 检查SC202CS传感器
s32 sc202cs_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{
    if (!iic) {
        iic = dev_open("iic0", &_hw_iic);
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    sc202cs_mipi_reset(isp_dev);

    if (0 != sc202cs_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

// sc202cs传感器初始化
void resetStatic();
s32 sc202cs_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    sc202cs_mipi_config_SENSOR(width, height, format, frame_freq);

    wr_sc202cs_reg(SC202CS_STREAM_CTRL_REG, SC202CS_STREAM_ON);

    return 0;
}

// 模拟增益值控制表（AGAIN）- 基于数据手册V1.3 表2-4
static const struct {
    uint8_t reg_value;  // 写入 0x3e09
    float gain;         // 增益倍数
    float gain_db;      // dB 值
    u16 gain_1024;      // 1024基准的增益值
} sc202cs_again_table[] = {
    {0x00, 1.000f,  0.00f,  1024},
    {0x01, 2.000f,  6.02f,  2048},
    {0x03, 4.000f, 12.04f,  4096},
    {0x07, 8.000f, 18.06f,  8192},
    {0x0F, 16.000f, 24.08f, 16384},
};

// 数字增益值控制表（DGAIN）- 基于数据手册V1.3 表2-5
static const struct {
    uint8_t dgain;         // 写入 0x3e06
    uint8_t dgain_fine;    // 写入 0x3e07
    float gain;            // 总增益倍数
    float gain_db;         // dB 值
    u16 gain_1024;         // 1024基准的增益值
} sc202cs_dgain_table[] = {
    {0x00, 0x80, 1.000f, 0.00f, 1024}, {0x00, 0x84, 1.031f, 0.27f, 1056},
    {0x00, 0x88, 1.063f, 0.53f, 1088}, {0x00, 0x8c, 1.094f, 0.78f, 1120},
    {0x00, 0x90, 1.125f, 1.02f, 1152}, {0x00, 0x94, 1.156f, 1.26f, 1184},
    {0x00, 0x98, 1.188f, 1.49f, 1216}, {0x00, 0x9c, 1.219f, 1.72f, 1248},
    {0x00, 0xa0, 1.250f, 1.94f, 1280}, {0x00, 0xa4, 1.281f, 2.15f, 1312},
    {0x00, 0xa8, 1.313f, 2.36f, 1344}, {0x00, 0xac, 1.344f, 2.57f, 1376},
    {0x00, 0xb0, 1.375f, 2.77f, 1408}, {0x00, 0xb4, 1.406f, 2.96f, 1440},
    {0x00, 0xb8, 1.438f, 3.15f, 1472}, {0x00, 0xbc, 1.469f, 3.34f, 1504},
    {0x00, 0xc0, 1.500f, 3.52f, 1536}, {0x00, 0xc4, 1.531f, 3.70f, 1568},
    {0x00, 0xc8, 1.563f, 3.88f, 1600}, {0x00, 0xcc, 1.594f, 4.05f, 1632},
    {0x00, 0xd0, 1.625f, 4.22f, 1664}, {0x00, 0xd4, 1.656f, 4.38f, 1696},
    {0x00, 0xd8, 1.688f, 4.54f, 1728}, {0x00, 0xdc, 1.719f, 4.70f, 1760},
    {0x00, 0xe0, 1.750f, 4.86f, 1792}, {0x00, 0xe4, 1.781f, 5.01f, 1824},
    {0x00, 0xe8, 1.813f, 5.17f, 1856}, {0x00, 0xec, 1.844f, 5.31f, 1888},
    {0x00, 0xf0, 1.875f, 5.46f, 1920}, {0x00, 0xf4, 1.906f, 5.60f, 1952},
    {0x00, 0xf8, 1.938f, 5.74f, 1984}, {0x00, 0xfc, 1.969f, 5.88f, 2016},

    {0x01, 0x80, 2.000f, 6.02f, 2048}, {0x01, 0x84, 2.063f, 6.29f, 2112},
    {0x01, 0x88, 2.125f, 6.55f, 2176}, {0x01, 0x8c, 2.188f, 6.80f, 2240},
    {0x01, 0x90, 2.250f, 7.04f, 2304}, {0x01, 0x94, 2.313f, 7.28f, 2368},
    {0x01, 0x98, 2.375f, 7.51f, 2432}, {0x01, 0x9c, 2.438f, 7.74f, 2496},
    {0x01, 0xa0, 2.500f, 7.96f, 2560}, {0x01, 0xa4, 2.563f, 8.17f, 2624},
    {0x01, 0xa8, 2.625f, 8.38f, 2688}, {0x01, 0xac, 2.688f, 8.59f, 2752},
    {0x01, 0xb0, 2.750f, 8.79f, 2816}, {0x01, 0xb4, 2.813f, 8.98f, 2880},
    {0x01, 0xb8, 2.875f, 9.17f, 2944}, {0x01, 0xbc, 2.938f, 9.36f, 3008},
    {0x01, 0xc0, 3.000f, 9.54f, 3072}, {0x01, 0xc4, 3.063f, 9.72f, 3136},
    {0x01, 0xc8, 3.125f, 9.90f, 3200}, {0x01, 0xcc, 3.188f, 10.07f, 3264},
    {0x01, 0xd0, 3.250f, 10.24f, 3328}, {0x01, 0xd4, 3.313f, 10.40f, 3392},
    {0x01, 0xd8, 3.375f, 10.57f, 3456}, {0x01, 0xdc, 3.438f, 10.72f, 3520},
    {0x01, 0xe0, 3.500f, 10.88f, 3584}, {0x01, 0xe4, 3.563f, 11.04f, 3648},
    {0x01, 0xe8, 3.625f, 11.19f, 3712}, {0x01, 0xec, 3.688f, 11.33f, 3776},
    {0x01, 0xf0, 3.750f, 11.48f, 3840}, {0x01, 0xf4, 3.813f, 11.62f, 3904},
    {0x01, 0xf8, 3.875f, 11.77f, 3968}, {0x01, 0xfc, 3.938f, 11.90f, 4032},
};

// 设置模拟增益 - 使用数据手册标准增益表
static void set_again(u32 gain)
{
    if (cur_again == gain) {
        return;
    }

    cur_again = gain;

    u8 index = 0;
    u8 table_size = sizeof(sc202cs_again_table) / sizeof(sc202cs_again_table[0]);

    // 边界值处理
    if (gain <= SC202CS_AGAIN_MIN) {
        index = 0;
    } else if (gain >= SC202CS_AGAIN_MAX) {
        index = table_size - 1;
    } else {
        // 查找最接近的增益档位
        for (u8 i = 0; i < table_size - 1; i++) {
            if (gain >= sc202cs_again_table[i].gain_1024 &&
                gain < sc202cs_again_table[i + 1].gain_1024) {
                index = i;
                break;
            }
        }
    }

    // 写入模拟增益寄存器 0x3E09
    wr_sc202cs_reg(SC202CS_AGAIN_REG_3E09, sc202cs_again_table[index].reg_value);

#if DEBUG_AE
    printf("AGAIN: gain=%d index=%d reg_val=0x%02X gain_actual=%.3fx\n",
           gain, index, sc202cs_again_table[index].reg_value, sc202cs_again_table[index].gain);
#endif
}

// 设置数字增益 - 使用数据手册标准增益表
static void set_dgain(u32 dgain)
{
    if (cur_dgain == dgain) {
        return;
    }

    cur_dgain = dgain;

    u8 index = 0;
    u8 table_size = sizeof(sc202cs_dgain_table) / sizeof(sc202cs_dgain_table[0]);

    // 边界值处理
    if (dgain <= SC202CS_DGAIN_MIN) {
        index = 0;
    } else if (dgain >= SC202CS_DGAIN_MAX) {
        index = table_size - 1;
    } else {
        // 查找最接近的增益档位
        for (u8 i = 0; i < table_size - 1; i++) {
            if (dgain >= sc202cs_dgain_table[i].gain_1024 &&
                dgain < sc202cs_dgain_table[i + 1].gain_1024) {
                index = i;
                break;
            }
        }
    }

    // 写入数字增益寄存器 0x3E06 (粗调) 和 0x3E07 (精调)
    wr_sc202cs_reg(SC202CS_DGAIN_REG, sc202cs_dgain_table[index].dgain);
    wr_sc202cs_reg(SC202CS_DGAIN_FINE_REG, sc202cs_dgain_table[index].dgain_fine);

#if DEBUG_AE
    printf("DGAIN: gain=%d index=%d dgain=0x%02X dgain_fine=0x%02X gain_actual=%.3fx\n",
           dgain, index, sc202cs_dgain_table[index].dgain,
           sc202cs_dgain_table[index].dgain_fine, sc202cs_dgain_table[index].gain);
#endif
}

// 计算模拟增益和数字增益分配 - 优化增益分配策略
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    // ISP增益值转换为传感器增益值
    gain = gain * GAIN_CONVERT_FACTOR / GAIN_CONVERT_BASE;

    // 优先使用模拟增益（噪声更低），模拟增益用完后使用数字增益
    if (gain <= SC202CS_AGAIN_MAX) {
        *_again = gain;
        *_dgain = SC202CS_DGAIN_MIN;  // 数字增益保持最小值(1x)
    } else {
        // 模拟增益达到最大值后, 剩余增益分配给数字增益
        *_again = SC202CS_AGAIN_MAX;
        u32 remaining_gain = (gain * SC202CS_DGAIN_MIN) / SC202CS_AGAIN_MAX;

        // 限制数字增益在有效范围内
        if (remaining_gain > SC202CS_DGAIN_MAX) {
            remaining_gain = SC202CS_DGAIN_MAX;
        }
        *_dgain = remaining_gain;
    }

#if DEBUG_AE
    printf("CALC_GAIN: total_gain=%d -> again=%d dgain=%d\n", gain, *_again, *_dgain);
#endif
}

// 设置VTS（垂直总行数/帧长）
static void set_vts(u32 vts)
{
    if (cur_vts == vts) {
        return;
    }

    // VTS范围限制（影响帧率：fps = PCLK*1000000/(HTS*VTS)）
    if (vts > SC202CS_MAX_VTS) {
        vts = SC202CS_MAX_VTS;
    }

    cur_vts = vts;

    // VTS寄存器16位分高低字节写入
    wr_sc202cs_reg(SC202CS_VTS_HIGH_REG, (vts >> 8) & 0xFF);
    wr_sc202cs_reg(SC202CS_VTS_LOW_REG, vts & 0xFF);
}

// 设置曝光时间
static void set_shutter(u32 texp)
{
    if (cur_expline == texp) {
        return;
    }

    cur_expline = texp;

    // 曝光时间寄存器16位分高低字节写入
    wr_sc202cs_reg(SC202CS_EXPOSURE_HIGH_REG, (texp >> 8) & 0xFF);
    wr_sc202cs_reg(SC202CS_EXPOSURE_LOW_REG, texp & 0xFF);

    // VTS控制逻辑
    u32 new_vts = texp + SC202CS_VTS_MARGIN;
    // set_vts(new_vts);
    if (new_vts <= 1360) {
        set_vts(1360);
    } else {
        if (new_vts >= 5200)
        {
            new_vts = 5200;
        }
        set_vts(new_vts);
    }
}

// 计算曝光参数
u32 sc202cs_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;

    // 将微秒曝光时间转换为行数（基于像素时钟和行长度）
#if SC202CS_FPS_VARIABLE
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#else
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#endif

    // 最小曝光行数限制
    if (texp < SC202CS_MIN_EXPOSURE_LINES) {
        texp = SC202CS_MIN_EXPOSURE_LINES;
    }

    // 计算实际对应的曝光时间（用于增益补偿）
    texp_align = texp * LINE_LENGTH_CLK / PCLK;

    // 如果实际曝光时间不足, 通过增益补偿
    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << RATIO_SHIFT_BITS) / texp_align;
    } else {
        ratio = (1 << RATIO_SHIFT_BITS);  // 无需补偿
    }

    // 填充ISP曝光参数结构体
    shutter->ae_exp_line = texp;
    shutter->ae_gain = (gain * ratio) >> RATIO_SHIFT_BITS;
    shutter->ae_exp_clk = 0;

    return 0;
}

// 设置传感器曝光参数
u32 sc202cs_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    u32 again, dgain;

    // 分解总增益为模拟增益和数字增益
    calc_gain(shutter->ae_gain, &again, &dgain);

    // 应用增益和曝光参数到传感器
    set_again(again);
    set_dgain(dgain);
    set_shutter(shutter->ae_exp_line);

#if DEBUG_AE
    // 计算实际曝光时间（用于调试显示）
    u32 exp_time_us = (shutter->ae_exp_line * LINE_LENGTH_CLK) / PCLK;

    // 寄存器回读验证
    u8 again_regs[6], dgain_high, dgain_low, itime_high, itime_low;
    rd_sc202cs_reg(SC202CS_AGAIN_REG_32A9, &again_regs[0]);
    rd_sc202cs_reg(SC202CS_AGAIN_REG_32AC, &again_regs[1]);
    rd_sc202cs_reg(SC202CS_AGAIN_REG_32AD, &again_regs[2]);
    rd_sc202cs_reg(SC202CS_AGAIN_REG_3211, &again_regs[3]);
    rd_sc202cs_reg(SC202CS_AGAIN_REG_3216, &again_regs[4]);
    rd_sc202cs_reg(SC202CS_AGAIN_REG_3217, &again_regs[5]);
    rd_sc202cs_reg(SC202CS_DGAIN_HIGH_REG, &dgain_high);
    rd_sc202cs_reg(SC202CS_DGAIN_LOW_REG, &dgain_low);
    rd_sc202cs_reg(SC202CS_EXPOSURE_HIGH_REG, &itime_high);
    rd_sc202cs_reg(SC202CS_EXPOSURE_LOW_REG, &itime_low);

    u16 dgain_readback = ((dgain_high & 0x0F) << 8) | dgain_low;
    u16 itime_readback = (itime_high << 8) | itime_low;

    // AE参数总览输出
    // printf("AE: gain=%d again=%d dgain=%d exp_lines=%d exp_time_us=%d\n",
    //            shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us);
    // 寄存器验证详细信息
    printf("AE: gain=%d again=%d dgain=%d exp_lines=%d exp_time_us=%d \nread: again[%02X,%02X,%02X,%02X,%02X,%02X=%d] dgain[%02X,%02X=%d] itime[%02X,%02X=%d]\n\n",
               shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us,
               again_regs[0], again_regs[1], again_regs[2], again_regs[3], again_regs[4], again_regs[5], again,
               dgain_high, dgain_low, dgain_readback, itime_high, itime_low, itime_readback);
#endif
    return 0;
}

// 传感器休眠（关闭视频流）
void sc202cs_mipi_sleep()
{
}

// 传感器唤醒（开启视频流）
void sc202cs_mipi_wakeup()
{
}

// 写寄存器接口（外部调用）
void sc202cs_mipi_wr_reg(u16 addr, u16 val)
{
    wr_sc202cs_reg(addr, (u8)val);
}

// 读寄存器接口（外部调用）
u16 sc202cs_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_sc202cs_reg(addr, &val);
    return val;
}

// 传感器设备注册
REGISTER_CAMERA(sc202cs_mipi) = {
    .logo               = "SC202CS",
    .isp_dev            = ISP_DEV_NONE,
    .in_format          = SEN_IN_FORMAT_RGGB,
    .out_format         = ISP_OUT_FORMAT_RAW,
    .mbus_type          = SEN_MBUS_CSI2,
    .mbus_config        = SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
#ifdef OUT_33_3FPS
    .fps                = 30,
    .real_fps           = (u32)(33.333333 * 65536),
#else
    .fps                = 15, // 30 // 15
#endif

    .sen_size           = {SC202CS_MIPI_OUTPUT_W, SC202CS_MIPI_OUTPUT_H},
    .isp_size           = {SC202CS_MIPI_OUTPUT_W, SC202CS_MIPI_OUTPUT_H},

#ifdef OUT_33_3FPS
    .cap_fps            = 30,
#else
    .cap_fps            = 15, // 30 // 15
#endif
    .sen_cap_size       = {SC202CS_MIPI_OUTPUT_W, SC202CS_MIPI_OUTPUT_H},
    .isp_cap_size       = {SC202CS_MIPI_OUTPUT_W, SC202CS_MIPI_OUTPUT_H},

    .ops                = {
        .avin_fps           = NULL,
        .avin_valid_signal  = NULL,
        .avin_mode_det      = NULL,
        .sensor_check       = sc202cs_mipi_check,
        .init               = sc202cs_mipi_init,
        .set_size_fps       = sc202cs_mipi_set_output_size,
        .power_ctrl         = sc202cs_mipi_power_ctl,

        .get_ae_params      = sc202cs_mipi_get_ae_params,
        .get_awb_params     = sc202cs_mipi_get_awb_params,
        .get_iq_params      = sc202cs_mipi_get_iq_params,

        .sleep              = sc202cs_mipi_sleep,
        .wakeup             = sc202cs_mipi_wakeup,
        .write_reg          = sc202cs_mipi_wr_reg,
        .read_reg           = sc202cs_mipi_rd_reg,
    }
};
