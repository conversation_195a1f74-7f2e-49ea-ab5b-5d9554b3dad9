/*
 * SC202CS 增益表测试程序
 * 用于验证基于数据手册V1.3的增益控制实现
 */

#include <stdio.h>
#include <stdint.h>

// 模拟增益值控制表（AGAIN）- 基于数据手册V1.3 表2-4
static const struct {
    uint8_t reg_value;  // 写入 0x3e09
    float gain;         // 增益倍数
    float gain_db;      // dB 值
    uint16_t gain_1024; // 1024基准的增益值
} sc202cs_again_table[] = {
    {0x00, 1.000f,  0.00f,  1024},
    {0x01, 2.000f,  6.02f,  2048},
    {0x03, 4.000f, 12.04f,  4096},
    {0x07, 8.000f, 18.06f,  8192},
    {0x0F, 16.000f, 24.08f, 16384},
};

// 数字增益值控制表（DGAIN）- 基于数据手册V1.3 表2-5 (部分)
static const struct {
    uint8_t dgain;         // 写入 0x3e06
    uint8_t dgain_fine;    // 写入 0x3e07
    float gain;            // 总增益倍数
    float gain_db;         // dB 值
    uint16_t gain_1024;    // 1024基准的增益值
} sc202cs_dgain_table[] = {
    {0x00, 0x80, 1.000f, 0.00f, 1024}, {0x00, 0x84, 1.031f, 0.27f, 1056},
    {0x00, 0x88, 1.063f, 0.53f, 1088}, {0x00, 0x8c, 1.094f, 0.78f, 1120},
    {0x00, 0x90, 1.125f, 1.02f, 1152}, {0x00, 0x94, 1.156f, 1.26f, 1184},
    {0x00, 0x98, 1.188f, 1.49f, 1216}, {0x00, 0x9c, 1.219f, 1.72f, 1248},
    {0x00, 0xa0, 1.250f, 1.94f, 1280}, {0x00, 0xa4, 1.281f, 2.15f, 1312},
    // ... 更多项省略，实际驱动中包含完整64项
};

// 测试模拟增益查表函数
void test_again_lookup(uint32_t gain_input)
{
    uint8_t index = 0;
    uint8_t table_size = sizeof(sc202cs_again_table) / sizeof(sc202cs_again_table[0]);
    
    printf("测试模拟增益: 输入增益=%d\n", gain_input);
    
    // 边界值处理
    if (gain_input <= 1024) {
        index = 0;
    } else if (gain_input >= 16384) {
        index = table_size - 1;
    } else {
        // 查找最接近的增益档位
        for (uint8_t i = 0; i < table_size - 1; i++) {
            if (gain_input >= sc202cs_again_table[i].gain_1024 && 
                gain_input < sc202cs_again_table[i + 1].gain_1024) {
                index = i;
                break;
            }
        }
    }
    
    printf("  -> 选择档位: index=%d, 寄存器值=0x%02X, 实际增益=%.3fx (%.2fdB)\n",
           index, sc202cs_again_table[index].reg_value, 
           sc202cs_again_table[index].gain, sc202cs_again_table[index].gain_db);
}

// 测试数字增益查表函数
void test_dgain_lookup(uint32_t gain_input)
{
    uint8_t index = 0;
    uint8_t table_size = sizeof(sc202cs_dgain_table) / sizeof(sc202cs_dgain_table[0]);
    
    printf("测试数字增益: 输入增益=%d\n", gain_input);
    
    // 边界值处理
    if (gain_input <= 1024) {
        index = 0;
    } else if (gain_input >= sc202cs_dgain_table[table_size-1].gain_1024) {
        index = table_size - 1;
    } else {
        // 查找最接近的增益档位
        for (uint8_t i = 0; i < table_size - 1; i++) {
            if (gain_input >= sc202cs_dgain_table[i].gain_1024 && 
                gain_input < sc202cs_dgain_table[i + 1].gain_1024) {
                index = i;
                break;
            }
        }
    }
    
    printf("  -> 选择档位: index=%d, 粗调=0x%02X, 精调=0x%02X, 实际增益=%.3fx (%.2fdB)\n",
           index, sc202cs_dgain_table[index].dgain, sc202cs_dgain_table[index].dgain_fine,
           sc202cs_dgain_table[index].gain, sc202cs_dgain_table[index].gain_db);
}

int main()
{
    printf("=== SC202CS 增益表测试程序 ===\n\n");
    
    printf("1. 模拟增益表测试:\n");
    test_again_lookup(1024);   // 1x
    test_again_lookup(1500);   // 1.5x (应该选择1x)
    test_again_lookup(2048);   // 2x
    test_again_lookup(3000);   // 3x (应该选择2x)
    test_again_lookup(4096);   // 4x
    test_again_lookup(8192);   // 8x
    test_again_lookup(16384);  // 16x
    test_again_lookup(20000);  // 超出范围 (应该选择16x)
    
    printf("\n2. 数字增益表测试:\n");
    test_dgain_lookup(1024);   // 1.000x
    test_dgain_lookup(1056);   // 1.031x
    test_dgain_lookup(1100);   // 1.1x (应该选择最接近的)
    test_dgain_lookup(1200);   // 1.2x
    test_dgain_lookup(1300);   // 1.3x
    
    printf("\n3. 增益范围总结:\n");
    printf("  模拟增益: 1x - 16x (5档)\n");
    printf("  数字增益: 1x - 3.938x (64档)\n");
    printf("  总增益范围: 1x - 63x (16 × 3.938)\n");
    
    return 0;
}
