#ifndef __SC202CS_MIPI_H__
#define __SC202CS_MIPI_H__

#include "typedef.h"

#define SC202CS_MIPI_OUTPUT_W    1280
#define SC202CS_MIPI_OUTPUT_H    720

#define SC202CS_FPS_VARIABLE    0


s32 sc202cs_mipi_set_output_size(u16 *width, u16 *height, u8 *freq);
s32 sc202cs_mipi_power_ctl(u8 isp_dev, u8 is_work);

s32 sc202cs_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);


void sc202cs_mipi_sleep();
void sc202cs_mipi_wakeup();
void sc202cs_mipi_wr_reg(u16 addr, u16 val);
u16 sc202cs_mipi_rd_reg(u16 addr);


#endif 
