#include "app_config.h"
#include "cpu.h"


static void reset_reason_check()
{
    u32 tmp = JL_CLOCK->RST_SRC;
    printf("reset reason:%x\n", tmp);
    if (tmp & BIT(0)) {
        printf("por reset\n");
    } else if (tmp & BIT(1)) {
        printf("vcm reset\n");
    } else if (tmp & BIT(2)) {
        printf("lvd reset\n");
    } else if (tmp & BIT(3)) {
        printf("wdt reset\n");
    } else if (tmp & BIT(4)) {
        printf("soft reset\n");
    }
    printf("\r\n");
}
void usb_tune_init(void)
{
    u8 SQ_TUNE        = 0b011;
    u8 DISC_TUNE      = 0b1000;
    u8 HS_PREEMP_EN   = 0b1;
    u8 HS_PREEMP_TUNE = 0b1;
    u8 HS_SLEW_TUNE   = 0b01;
    u8 HS_VREF_TUNE   = 0b1000;

    u8 DM_FSLS_PTUNE  = 0b0011;
    u8 DP_FSLS_PTUNE  = 0b0011;
    u8 DM_HSFS_RTUNE  = 0b1000;
    u8 DP_HSFS_RTUNE  = 0b1000;

    HUSB_COM_CON1 = 0;
    HUSB_COM_CON1 = (SQ_TUNE        <<  0) |
                    (DISC_TUNE      <<  3) |
                    (HS_PREEMP_EN   << 16) |
                    (HS_PREEMP_TUNE << 17) |
                    (HS_SLEW_TUNE   << 18) |
                    (HS_VREF_TUNE   << 20) ;

    HUSB_PHY0_CON3 = 0;
    HUSB_PHY0_CON3 = (DM_FSLS_PTUNE <<  0) |
                     (DM_HSFS_RTUNE <<  4) |
                     (DP_FSLS_PTUNE <<  8) |
                     (DP_HSFS_RTUNE << 12) ;
}


//test//
void fusb_test_io()
{
    HUSB_COM_CON0 |= BIT(0);	//common_bias_en
    HUSB_PHY0_CON0 |= BIT(2);	//p0_tran_en;
    HUSB_PHY0_CON0 |= BIT(3);	//p0_tx_bias_en;
    HUSB_PHY0_CON0 |= BIT(4);	//p0_rx_bias_en;

    HUSB_PHY0_CON0 |= BIT(16);  //fs tx en
    HUSB_PHY0_CON0 &= ~BIT(18);  //dm output 0
    HUSB_PHY0_CON0 |= BIT(17);   //do output 1

    while (1) {
        HUSB_PHY0_CON0 ^= (BIT(17) | BIT(18)); //
        JL_WDT->CON |= BIT(6);
    }

}
void husb_test_io()
{
    HUSB_COM_CON0 |= BIT(0);	//common_bias_en
    HUSB_PHY0_CON0 |= BIT(2);	//p0_tran_en;
    HUSB_PHY0_CON0 |= BIT(3);	//p0_tx_bias_en;
    HUSB_PHY0_CON0 |= BIT(4);	//p0_rx_bias_en;

    HUSB_PHY0_CON0 |= BIT(16);  //fs tx en
    HUSB_PHY0_CON0 &= ~(BIT(18) | BIT(17)); //dm dm output 0
    HUSB_PHY0_CON0 |= BIT(19);  //hs tx en
    delay(10);
    HUSB_PHY0_CON0 |= BIT(20);  //hs tx 1
    while (1) {
        HUSB_PHY0_CON0 ^= BIT(20); //hs tx 0/1
        delay(100);
        JL_WDT->CON |= BIT(6);
    }

}
void maskrom_init(void *pchar);
static u8 msg_pool_buf[512];
void setup_arch()
{
    int tmp;
    __asm__ volatile("%0 = icfg" : "=r"(tmp));
    tmp |= BIT(8);
    __asm__ volatile("icfg = %0" : : "r"(tmp));
    __asm__ volatile("sti");
    u8 trim_mode = 1;

    dvdd_ctl(4);
    avdd18_ctl(0, 0);
    avdd28_ctl(0, 0);

#if UART_DEBUG
    void uart_debug_init();
    uart_debug_init();
    maskrom_init(putchar);
#endif

    u32 get_osc_freq_cfg(void);
    volatile u8 ref_lrc = 1;
    u16 pll_nr = 0;
    int xosc_freq = get_osc_freq_cfg();
    if (xosc_freq >= 0) {
        // xosc ref clk
        ref_lrc = 0;
        xosc_sys_clk_init((u8)xosc_freq); //0:12m,1:1m,2:32k
    } else {

        // lrc ref clk
        u8 get_cur_trim_mode(void);
        trim_mode = get_cur_trim_mode();
        // lrc ref clk
        if (trim_mode == 0) {
            //old usb trim
            if (lrc_trim(&pll_nr)) { //软件trim
                //trim 失败
                pll_nr = 0;
            }

            lrc_2_xosc(1, 0);
            sys_clk_reinit(pll_nr);
        } else {
            // new usb trim
            u32 get_pll_trim_from_flash(void);
            sysclk_init(1, get_pll_trim_from_flash()); //switch sys clk to 64M
        }
    }

    printf("chip id 0x%x chip_version:%d\n", get_chip_id(), get_chip_version());
    if (trim_mode == 0) {
        printf("use old usb trim pll nr:%d\n", pll_nr);
    }

    reset_reason_check();


    efuse_info_dump();

    ENABLE_INT();

    //异常中断初始化
    exception_init();

    /* wdt_init(); */

    msg_pool_init(msg_pool_buf, sizeof(msg_pool_buf)); //消息池


#if TCFG_PC_ENABLE

    usb_tune_init();

    if (ref_lrc == 1) { //lrc 作为参考时钟
        if (trim_mode) { //new usb trim
            lrc_2_xosc(1, 0);
            extern void lrc_trim_init();
            lrc_trim_init();
        }
    }

    /* fusb_test_io(); */
    /* husb_test_io(); */
    void usb_slave_start();
    usb_slave_start(); // 进入usb从机模式

    /* lrc_trim_wait_done(); */
    /* pll_nr = lrc_trim_get_pll_nr(); */

#else
    _sys_clk_reinit();
#endif
    sys_tick_init();

    resfile_init(); /* 资源文件加载 */

}

u32 user_sys_clk_cfg(void)
{
#if defined CONFIG_NLPFIX_ENABLE || defined CONFIG_UVC_FOR_1080P_ENABLE
    dvdd_ctl(6);
    return 160000000;
#endif
    return 0;
}
