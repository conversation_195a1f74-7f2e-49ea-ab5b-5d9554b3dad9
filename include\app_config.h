#ifndef  __APP_CONFIG_H__
#define  __APP_CONFIG_H__

#ifndef __LD__
#include "iic.h"
#include "app_msg.h"
#endif

//*********************************************************************************//
//                      product configure, can only select one
// defined by <PERSON>file according to selected product
// #define CONFIG_PRODUCT_SU7PRO_DEFAULT
// #defined CONFIG_PRODUCT_SU7PRO_2ND
// #define CONFIG_PRODUCT_YU7_DEFAULT

// product of S30
// #define CONFIG_PRODUCT_S30_OUTPUT_864x480
// #define CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0
// #define CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA
// #define CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG
//*********************************************************************************//
//                      app case configuare
//                     (由config.mk自动生成)
// #define CONFIG_ENABLE_OSD
#if defined(CONFIG_PRODUCT_YU7_BULK) || defined(CONFIG_PRODUCT_SU7Z_BULK)
#define CONFIG_ENABLE_BULK_MODE
#endif
//*********************************************************************************//
#ifdef CONFIG_ENABLE_BULK_MODE
#define CAR_CAM_CASE
#else
#define PC_CAM_CASE
#endif
//*********************************************************************************//
#ifndef CONFIG_PRODUCT_SU7Z_BULK
#define FILL_LIGHT_ENABLE //补光灯 0 function off, 1 function on
#endif
#define FLIP         1  ////1 为正向，0为反向

#if defined(CONFIG_PRODUCT_S30_OUTPUT_864x480) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)
   #define CONFIG_IS_PRODUCT_S30 1
#else
   #define CONFIG_IS_PRODUCT_S30 0
#endif

#if defined(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG) \
   || defined(CONFIG_PRODUCT_YU7_VM0)
   #define CONFIG_COMPATIBLE_YIKANG_VM0 1
#else
   #define CONFIG_COMPATIBLE_YIKANG_VM0 0
#endif

#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT) || defined(CONFIG_PRODUCT_SU7PRO_2ND) || defined(CONFIG_PRODUCT_SU7Z_BULK) 
    #define VERSION_PREFIX "SU7"
    #define VERSION_STR "V1.0.7"
#elif (CONFIG_IS_PRODUCT_S30)
    #define VERSION_PREFIX "S30"
    #define VERSION_STR "V1.0.4"
#elif defined(CONFIG_PRODUCT_YU7_DEFAULT) || defined(CONFIG_PRODUCT_YU7_VM0) || defined(CONFIG_PRODUCT_YU7_BULK)
    #define VERSION_PREFIX "YU7"
    #define VERSION_STR "V1.0.8"
#elif defined(CONFIG_PRODUCT_YU7L)
    #define VERSION_PREFIX "EVB"
    #define VERSION_STR "V1.0.0"
#else
    #error "should define at least one product"
#endif

#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT) || defined(CONFIG_PRODUCT_SU7PRO_2ND) 
    #define VERSION_SUFFIX "H63P_ISOC"
#elif defined(CONFIG_PRODUCT_SU7Z_BULK)
    #define VERSION_SUFFIX "H63P_BULK"
#elif defined (CONFIG_PRODUCT_YU7_DEFAULT)
    #define VERSION_SUFFIX "1346_ISOC"
#elif defined (CONFIG_PRODUCT_YU7_BULK)
    #define VERSION_SUFFIX "1346_BULK"
#elif defined (CONFIG_PRODUCT_YU7_VM0)
    #define VERSION_SUFFIX "1346_VM0"
#elif defined (CONFIG_PRODUCT_S30_OUTPUT_864x480)
    #define VERSION_SUFFIX "ISOC_864"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0)
    #define VERSION_SUFFIX "ISOC_VM0"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)
    #define VERSION_SUFFIX "ISOC_XFGJ"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)
    #define VERSION_SUFFIX "ISOC_XSUN"
#elif defined (CONFIG_PRODUCT_YU7L)
    #define VERSION_SUFFIX "ISOC_EVB"
#else
    #error "should define at least one product"
#endif
#define SOFTWARE_VERSION_PREFIX VERSION_PREFIX"_"VERSION_STR"_"VERSION_SUFFIX
//*********************************************************************************//
//                           board configuare
//*********************************************************************************//
#define CONFIG_BOARD_AC5316A_DEV_20211231

//*********************************************************************************//
//                            function config
//*********************************************************************************//
#ifdef CONFIG_BOARD_AC5316A_DEV_20211231
    #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS | AUDIO_CLASS)
    // #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS | MIC_CLASS)
    // #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS)
    /////////// USB MODE
    #ifdef CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
    #elif defined CONFIG_PRODUCT_S30_OUTPUT_864x480
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
    #elif defined CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA
        // #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define CONFIG_USE_YUV420
        #define UAC_ISO_INTERVAL 1
    #elif defined CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define UAC_ISO_INTERVAL 1
    #else // YU7 and SU7
        #define HUSB_MODE
        #define CONFIG_NLPFIX_ENABLE
    #endif

    #if (CONFIG_COMPATIBLE_YIKANG_VM0) && defined(CONFIG_NLPFIX_ENABLE)
        #undef CONFIG_NLPFIX_ENABLE
    #endif
    // #define CONFIG_YUYV_ENABLE                 //yuyv uvc enable
    // #define CONFIG_ADKEY_ENABLE                //ADKEY 使能
    // #define CONFIG_IOKEY_ENABLE                //IOKEY 使能
    // #define CONFIG_UVC_FOR_1080P_ENABLE        //1080p插值功能
#endif

#ifdef USE_FLASH_CFG_EN
#ifndef CONFIG_BOARD_AC532X_DEV_20211231
#error "please define CONFIG_BOARD_AC532X_DEV_20211231  for vga car cam case !!!"
#endif
#endif

#if defined CONFIG_NLPFIX_ENABLE && defined CONFIG_UVC_FOR_1080P_ENABLE
#error "can not Supports 1080p and nlpfix at the same time!!"
#endif



#if defined CONFIG_NLPFIX_ENABLE || defined CONFIG_UVC_FOR_1080P_ENABLE
#define CONFIG_UVC_ISO_REDIRECT_ENABLE
#endif

#define SD0_ROOT_PATH "storage/sd0/C/"

#include "usb_std_class_def.h"
#define TCFG_PC_ENABLE          1
#include "usb_common_def.h"

#endif

